package com.yts.yyt.ticket.api.exception;

import cn.hutool.core.util.StrUtil;
import com.yts.yyt.common.core.exception.GlobalBizException;

public class TicketException extends GlobalBizException {


    public TicketException() {
        super();
    }

    public TicketException(String msg) {
        super(msg);

    }

    public TicketException(String msg, Throwable e) {
        super(msg, e);
    }

    public TicketException(String msg, int code) {
        super(msg, code);

    }

    public TicketException(TicketErrorEnum codeEnum) {
        super(codeEnum.getMsg(), codeEnum.getCode());
    }

    public TicketException(Integer code, String msg) {
        super(msg, code);

    }

    public TicketException(String msg, int code, Throwable e) {
        super(msg, code, e);
    }

    public static TicketException build(TicketErrorEnum codeEnum) {
        return new TicketException(codeEnum.getCode(), codeEnum.getMsg());
    }

    public static TicketException build(TicketErrorEnum codeEnum,String msg) {
        return new TicketException(codeEnum.getCode(), StrUtil.format(codeEnum.getMsg(),msg));
    }

}
