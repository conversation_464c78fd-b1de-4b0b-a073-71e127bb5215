package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("ticket_info")
@Schema(description = "门票信息表")
public class TicketInfoEntity  {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
	@TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 门票类型(person-成人票、group-团体票)
     */
    @Schema(description="门票类型(person-成人票、group-团体票)")
    private String type;
    /**
     * 门票单价
     */
    @Schema(description="门票单价")
    private BigDecimal unitPrice;
    /**
     * 最少购票数
     */
    @Schema(description="最少购票数")
    private Integer maxNums;
    /**
     * 最多购票数
     */
    @Schema(description="最多购票数")
    private Integer minNums;
    /**
     * 参观日期
     */
    @Schema(description="参观日期")
    private LocalDate visitDate;
    /**
     * 总票数
     */
    @Schema(description="总票数")
    private Integer totalQuantity;
    /**
     * 门票样式图片URL
     */
    @Schema(description="门票样式图片URL")
    private String ticketImage;
    /**
     * 状态(0-启用，1-禁用)
     */
    @Schema(description="状态(0-启用，1-禁用)")
    private Integer status;
    /**
     * 创建人ID
     */
    @Schema(description="创建人ID")
    private Long createById;
    /**
     * 创建人姓名
     */
    @Schema(description="创建人姓名")
    private String createBy;

	@Schema(description = "创建时间")
	@TableField(fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	@Schema(description = "修改时间")
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;
	/**
	 * 删除标志 0-正常、1-删除
	 */
	@Schema(description = "删除标志 0-正常、1-删除")
	private Integer delFlag;


}

