package com.yts.yyt.ticket.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.ticket.api.dto.TicketOrderRefundCallbackDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteTicketRefundService", value = ServiceNameConstants.TICKET_SERVER, path = "/refund")
public interface RemoteTicketRefundService {

	/**
	 * 票务订单退款回调
	 *
	 * @param dto 回调信息
	 * @return 是否回调完成
	 */
	@PostMapping("/callback")
	@NoToken
	R<Boolean> refundCallback(@RequestBody TicketOrderRefundCallbackDTO dto);

}