package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 旅拍订单退款DTO
 */
@Data
@Schema(description = "旅拍订单退款DTO")
public class TravelTicketRefundDTO {

    @NotNull(message = "订单id不能为空")
    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "退款原因")
    private String refundReason;
} 