package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("ticket_order")
@Schema(description = "票务订单表")
public class TicketOrderEntity extends Model<TicketOrderEntity> {

	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description = "主键")
	private Long id;

	@Schema(description = "订单编号")
	private String orderNo;

	/**
	 * 订单来源 1-小程序 2-APP
	 * @see com.yts.yyt.ticket.api.enums.OrderEnum.OrderSource
	 */
	@Schema(description = "订单来源 1-小程序 2-APP")
	private Integer orderSource;

	@Schema(description = "微信交易id")
	private String transactionId;

	@Schema(description = "支付订单号 关联 PayTradeOrder表中orderId字段")
	private String payOrderId;

	@Schema(description = "支付流水号")
	private String paySerio;

	/**
	 * 退款流水号
	 */
	@Schema(description="退款流水号")
	private String refundSerio;

	@Schema(description = "订单类型")
	private String orderType;

	@Schema(description = "用户ID")
	private Long userId;

	@Schema(description = "用户姓名")
	private String name;

	@Schema(description = "用户手机号")
	private String mobile;

	@Schema(description = "门票ID")
	private Long ticketDetailId;

	@Schema(description = "商品编号")
	private String goodsNo;

	@Schema(description = "下单时间")
	private LocalDateTime orderTime;

	@Schema(description = "支付时间")
	private LocalDateTime paymentTime;

	@Schema(description = "退款时间")
	private LocalDateTime refundTime;

	@Schema(description = "过期时间")
	private LocalDateTime expiredTime;

	@Schema(description = "核销时间")
	private LocalDateTime verificationTime;

	@Schema(description = "核销人手机号")
	private String verificationPhone;

	@Schema(description = "核销人用户id")
	private String verificationUserId;

	@Schema(description = "门票数量")
	private Integer ticketQuantity;

	@Schema(description = "门票单价")
	private BigDecimal unitPrice;

	@Schema(description = "订单总金额")
	private BigDecimal totalAmount;

	@Schema(description = "应付金额")
	private BigDecimal payableAmount;

	@Schema(description = "退款金额")
	private BigDecimal refundAmount;

	@Schema(description = "订单状态(1-待支付 |2-已支付(待核销) |3-已使用（已核销） |4-已过期 |5-已关闭 |6-退款中 |7-已退款)")
	private Integer status;

	@Schema(description = "联系人手机号")
	private String contactPhone;

	/**
	 * @see com.yts.yyt.ticket.api.enums.OrderEnum.OrderPayType
	 */
	@Schema(description = "支付方式:1-微信小程序 2-微信 3-支付宝 4-银联")
	private Integer payType;

	@Schema(description = "参观日期")
	private LocalDate visitDate;

	@Schema(description = "参观开始时间")
	private String startTime;

	@Schema(description = "参观结束时间")
	private String endTime;

	@Schema(description = "门票二维码图片url")
	private String qrCodeImg;

	@Schema(description = "防伪码")
	private String antiFakeCode;

	@Schema(description = "创建时间")
	@TableField(fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	@Schema(description = "修改时间")
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

	/**
	 * 删除标志 0-正常、1-删除
	 */
	@Schema(description = "删除标志 0-正常、1-删除")
	private Integer delFlag;

	@Schema(description = "备注")
	private String remark;

}

