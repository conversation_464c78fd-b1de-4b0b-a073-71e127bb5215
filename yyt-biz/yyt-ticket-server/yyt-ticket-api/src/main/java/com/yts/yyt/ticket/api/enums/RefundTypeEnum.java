package com.yts.yyt.ticket.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundTypeEnum {

    /**
     * 用户申请退款
     */
    USER_APPLY(1, "用户申请退款"),

    /**
     * 系统过期退款
     */
    SYSTEM_EXPIRED(2, "系统过期退款");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 退款类型枚举
     */
    public static RefundTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RefundTypeEnum refundType : values()) {
            if (refundType.getCode().equals(code)) {
                return refundType;
            }
        }
        return null;
    }

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        RefundTypeEnum refundType = getByCode(code);
        return refundType != null ? refundType.getDesc() : null;
    }
} 