package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 管理端门票统计查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端门票统计查询参数")
public class TicketAdminStatisticsDTO extends BasePage {

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @Schema(description = "开始日期", example = "2023-01-01")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @Schema(description = "结束日期", example = "2023-12-31")
    private LocalDate endDate;

    /**
     * 门票类型
     */
    @Schema(description = "门票类型，可选过滤条件", example = "普通票")
    private String type;

}