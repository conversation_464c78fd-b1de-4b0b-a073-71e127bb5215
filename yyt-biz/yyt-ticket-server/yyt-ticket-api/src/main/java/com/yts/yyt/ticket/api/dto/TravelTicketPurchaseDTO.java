package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.ticket.api.enums.OrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 旅拍票购买DTO
 */
@Data
@Schema(description = "旅拍票购买DTO")
public class TravelTicketPurchaseDTO {

	@NotNull(message = "旅拍票id不能为空")
	@Schema(description = "旅拍票id")
	private Long ticketId;

	@NotBlank(message = "联系人手机号不能为空")
	@Schema(description = "联系人手机号")
	private String contactPhone;

	@NotNull(message = "购买数量不能为空")
	@Schema(description = "购买数量")
	private Integer ticketQuantity;

	@Schema(description = "订单来源 1-小程序 2-APP", implementation = OrderEnum.OrderSource.class)
	@NotNull(message = "订单来源不能为空")
	private Integer orderSource;
} 