package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("travel_ticket")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "旅拍门票信息表")
public class TravelTicketEntity  extends Model<TravelTicketEntity>  {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 标题
     */
    @Schema(description="标题")
    private String title;
    /**
     * 门票单价
     */
    @Schema(description="门票单价")
    private BigDecimal unitPrice;
    /**
     * 门票原价
     */
    @Schema(description="门票原价")
    private BigDecimal originalPrice;
    /**
     * 门票样式图片URL
     */
    @Schema(description="门票样式图片URL")
    private String ticketImage;
    /**
     * 内容
     */
    @Schema(description="内容")
    private String content;
    /**
     * 状态(0-启用，1-禁用)
     */
    @Schema(description="状态(0-启用，1-禁用)")
    private Integer status;
    /**
     * 创建人姓名
     */
    @Schema(description="创建人姓名")
    private String createBy;
    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 删除标记(0-正常,1-已删除)
     */
    @Schema(description="删除标记(0-正常,1-已删除)")
    @TableLogic
    private Integer delFlag;

}

