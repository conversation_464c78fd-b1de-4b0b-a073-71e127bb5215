package com.yts.yyt.ticket.api.enums;

import lombok.Getter;

/**
 * 票务订单状态枚举类
 */
@Getter
public enum TicketOrderStatusEnum {

    PENDING_PAYMENT(1, "待支付"),
    PAID_UNVERIFIED(2, "已支付(待核销)"),
    USED_VERIFIED(3, "已使用（已核销）"),
    EXPIRED(4, "已过期"),
    CLOSED(5, "已关闭"),
    REFUNDING(6, "退款中"),
    REFUNDED(7, "退票/退款");

    private final Integer code;
    private final String desc;

    TicketOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取description
     * @param code 状态码
     * @return 描述信息
     */
    public static String getDescByCode(Integer code) {
        for (TicketOrderStatusEnum statusEnum : TicketOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}
