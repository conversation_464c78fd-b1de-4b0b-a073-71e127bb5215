package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 票务订单获取支付参数-入参
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "票务订单获取支付参数-入参")
public class TicketPayParameterDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	@NotNull(message = "订单id不能为空")
	@Schema(description = "订单id")
	private Long orderId;

	@Schema(description = "微信小程序用户openId")
	private String openId;

}