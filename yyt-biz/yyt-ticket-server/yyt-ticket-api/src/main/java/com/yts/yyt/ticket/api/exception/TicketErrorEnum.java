package com.yts.yyt.ticket.api.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TicketErrorEnum {

	PARAM_ERROR(89991, "参数错误"),
	SYSTEM_ERROR(89999, "系统异常,请稍后重试"),

	TICKET_NOT_EXIST(80001, "门票信息不存在"),
	TICKET_TIME_CONFLICT(80002, "日期%s的时间段%s~%s与已有门票时间段冲突"),
	TICKET_STATUS_ERROR(80003, "门票状态错误，只能在禁用状态下编辑"),
	TICKET_QUANTITY_ERROR(80004, "时段%s~%s总数不能小于已售数"),
	TICKET_ORDER_NOT_EXIST(80005, "门票订单信息不存在"),

	DATE_PARAMS_ERROR(80100, "日期格式错误"),
	TICKET_QUANTITY_INVALID(80101, "购买数量必须大于0"),
	CONTACT_PHONE_INVALID(80102, "联系人手机号格式不正确"),
	VISITOR_COUNT_MISMATCH(80103, "游客人数与购票数量不匹配"),
	VISITOR_ID_CARD_INVALID(80104, "身份证号码格式不正确"),
	TICKET_STATUS_INVALID(80105, "门票已下架"),
	TICKET_STOCK_INSUFFICIENT(80106, "门票库存不足"),
	TICKET_MIN_QUANTITY_LIMIT(80107, "购买数量不能小于最小限制"),
	TICKET_MAX_QUANTITY_LIMIT(80108, "购买数量不能超过最大限制"),
	TICKET_EXPIRED(80109, "门票已过期"),
	ORDER_QUERY_FAILED(80110, "查询订单失败"),
	ORDER_PERMISSION_DENIED(80111, "无权操作该订单"),
	ORDER_NOT_EXIST(80112, "订单不存在"),
	ORDER_STATUS_ERROR(80113, "订单状态异常"),
	ADJUST_AMOUNT_ERROR(80114, "调整金额不能小于等于0"),
	ORDER_STATUS_ERROR_ADJUST_AMOUNT(80115, "当前订单状态不允许调整价格"),
	REFUND_NOT_ALLOWED(80116,"参观前{}小时内不允许退票"),

	TICKET_EXIST(80117, "门票信息已存在"),
    VISITOR_EXIST(80118, "游客信息已存在"),
	TICKET_CREATE_CONFLICT(80119, "日期%s的%s门票已存在，不能重复创建"),
	TICKET_DELETE_ERROR(80120, "时段%s~%s已有售出票数，不能删除"),
	ORDER_STATUS_NOT_VERIFICATION(80121, "该订单状态不允许核销"),
	EXPORT_FAILED(80122, "导出失败,请重试"),
	ID_CARD_QUERY_ERROR(80123, "获取用户身份证信息失败"),
	TRADE_CONFIG_ERROR(80124, "获取交易配置失败"),
	TICKET_TIME_SLOT_CANNOT_EDIT(80126, "时间段不可编辑"),
	GET_TICKET_PARAMS_ERROR(80125, "获取票务订单支付参数失败"),
	TICKET_HAS_ORDER(80126, "存在关联订单，不可删除"),



	;

    private final Integer code;
    private final String msg;

}