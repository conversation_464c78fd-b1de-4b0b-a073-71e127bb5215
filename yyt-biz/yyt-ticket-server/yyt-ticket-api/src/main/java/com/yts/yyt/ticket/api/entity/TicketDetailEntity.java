package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("ticket_detail")
@Schema(description = "门票时段信息表")
public class TicketDetailEntity {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    private Long id;
    /**
     * 门票id
     */
    @Schema(description="门票id")
    private Long ticketId;
    /**
     * 门票类型(person-成人票、group-团体票)
     */
    @Schema(description="门票类型(person-成人票、group-团体票)")
    private String type;
    /**
     * 参观日期
     */
    @Schema(description="参观日期")
    private LocalDate visitDate;
    /**
     * 时段开始时间
     */
    @Schema(description="时段开始时间")
    private String startTime;
    /**
     * 时段结束时间
     */
    @Schema(description="时段结束时间")
    private String endTime;
    /**
     * 总票数
     */
    @Schema(description="总票数")
    private Integer totalQuantity;
    /**
     * 剩余票数
     */
    @Schema(description="剩余票数")
    private Integer remainingQuantity;
    /**
     * 状态(0-启用，1-禁用)
     */
    @Schema(description="状态(0-启用，1-禁用)")
    private Integer status;
    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	 * 删除标志 0-正常、1-删除
	 */
	@Schema(description = "删除标志 0-正常、1-删除")
	private Integer delFlag;

}

