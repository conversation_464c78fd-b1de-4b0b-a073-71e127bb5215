package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理端旅拍订单列表查询DTO
 */
@Data
@Schema(description = "管理端旅拍订单列表查询DTO")
public class TravelTicketAdminOrderListDTO {

    @Schema(description = "当前页码")
    private Integer current = 1;

    @Schema(description = "每页大小")
    private Integer size = 10;

    @Schema(description = "订单状态(1-待支付 |2-已支付(待核销) |3-已使用（已核销） |4-已过期 |5-已关闭 |6-退款中 |7-已退款)")
    private Integer status;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "联系人手机号")
    private String mobile;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
} 