package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理端门票订单列表查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端门票订单列表查询参数")
public class TicketAdminOrderListDTO {

	@Schema(description = "订单号")
	private String orderNo;
	@Schema(description = "交易流水号")
	private String paySerio;
	@Schema(description = "门票类型")
	private String type;
	@Schema(description = "买家姓名")
	private String name;
	@Schema(description = "买家手机")
	private String mobile;

	@Schema(description = "订单状态：0-待支付 1-已支付 2-已完成 3-已取消 4-已退款")
	private Integer status;

	@Schema(description = "开始时间", example = "2023-06-01 00:00:00")
	private LocalDateTime startTime;

	@Schema(description = "结束时间", example = "2023-06-30 23:59:59")
	private LocalDateTime endTime;

	@NotNull(message = "当前页码不能为空")
	@Min(value = 1, message = "当前页码不能小于1")
	@Schema(description = "当前页码", example = "1")
	private Integer current = 1;

	@NotNull(message = "每页大小不能为空")
	@Min(value = 1, message = "每页大小不能小于1")
	@Schema(description = "每页大小", example = "10")
	private Integer size = 10;
} 