package com.yts.yyt.ticket.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.ticket.api.dto.TicketOrderPayCallbackDTO;
import com.yts.yyt.ticket.api.dto.TicketPayParameterDTO;
import com.yts.yyt.ticket.api.vo.TicketPayParameterVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(contextId = "remoteTicketPayService", value = ServiceNameConstants.TICKET_SERVER, path = "/pay")
public interface RemoteTicketPayService {

	/**
	 * 票务订单回调
	 *
	 * @param dto 回调信息
	 * @return 是否回调完成
	 */
	@PostMapping("/callback")
	@NoToken
	R<Boolean> payCallback(@RequestBody TicketOrderPayCallbackDTO dto);

	/**
	 * 票务订单支付参数
	 *
	 * @param dto dto
	 * @return 票务订单支付参数
	 */
	@PostMapping("/params")
	@NoToken
	R<TicketPayParameterVO> getOrderPayParams(@RequestBody TicketPayParameterDTO dto);
}