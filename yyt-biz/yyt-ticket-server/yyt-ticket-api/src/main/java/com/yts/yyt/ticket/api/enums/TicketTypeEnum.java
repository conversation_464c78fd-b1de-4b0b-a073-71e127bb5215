package com.yts.yyt.ticket.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 门票类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TicketTypeEnum{

    /**
     * 成人票
     */
    PERSON("person", "成人票"),

    /**
     * 团体票
     */
    GROUP("group", "团体票");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TicketTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TicketTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

	public static String getDescByCode(String ticketType) {
		for (TicketTypeEnum item : values()) {
			if (item.getCode().equals(ticketType)) {
				return item.getDesc();
			}
		}
		return "";
	}
}