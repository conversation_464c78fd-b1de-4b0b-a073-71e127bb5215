package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 票务核销列表查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "票务核销列表查询参数")
public class TicketVerificationListDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1, message = "当前页码不能小于1")
    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小不能小于1")
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
} 