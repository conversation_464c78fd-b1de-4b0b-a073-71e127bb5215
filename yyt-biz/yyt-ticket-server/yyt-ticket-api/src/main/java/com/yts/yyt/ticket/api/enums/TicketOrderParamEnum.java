package com.yts.yyt.ticket.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单参数枚举
 */
@AllArgsConstructor
@Getter
public enum TicketOrderParamEnum {

    TICKET_REFUND_FEE_RATE("TICKET_REFUND_FEE_RATE","门票退票手续费率（%）","10"),
	TICKET_CANNOT_REFUND_CONDITION("TICKET_CANNOT_REFUND_CONDITION","不可退票条件","24"),
	TICKET_FULL_REFUND_CONDITION("TICKET_FULL_REFUND_CONDITION","全额退票条件","48"),
	TRAVEL_ORDER_VALIDITY_PERIOD("TRAVEL_ORDER_VALIDITY_PERIOD","旅拍订单有效期","30"),
    ;

    private final String key;
    private final String desc;
    private final String defaultValue;

}
