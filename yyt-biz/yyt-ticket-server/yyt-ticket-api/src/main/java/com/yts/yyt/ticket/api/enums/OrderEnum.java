package com.yts.yyt.ticket.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface OrderEnum {
	/**
	 * 订单来源
	 */
	@AllArgsConstructor
	@Getter
	public static enum OrderSource {
		APPLET(1, "小程序"),
		H5(2, "ANDROID"),
		PC(3, "IOS"),
		;

		private final int type;

		private final String desc;

		public static boolean contains(int type) {
			for (OrderSource status : OrderSource.values()) {
				if (status.getType() == type) {
					return true;
				}
			}
			return false;
		}

	}

	@AllArgsConstructor
	@Getter
	public static enum OrderPayType {
		MPWECHAT_PAY(1, "微信小程序"),
		WECHAT_PAY(2, "微信"),
		ALI_PAY(3, "支付宝"),
		PAYMENT_COMPANY_PAY(4, "三方支付"),
		OFFLINE_PAY(5, "线下支付"),
		;

		private final int type;

		private final String desc;

		public static boolean contains(int type) {
			for (OrderPayType status : OrderPayType.values()) {
				if (status.getType() == type) {
					return true;
				}
			}
			return false;
		}

		public static String getByType(Integer type) {
			if (type == null) {
				return null;
			}
			for (OrderPayType item : values()) {
				if (item.getType() == type) {
					return item.getDesc();
				}
			}
			return null;
		}
	}

	@AllArgsConstructor
	@Getter
	public static enum OrderPayStatus {
		WAIT_PAY(0, "未支付"),
		SUCCESS(1, "支付成功"),
		FAILED(2, "支付失败"),
		;

		private final int type;

		private final String desc;

	}
}


