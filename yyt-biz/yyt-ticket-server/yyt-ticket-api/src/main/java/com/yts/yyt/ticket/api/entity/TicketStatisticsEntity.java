package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("ticket_statistics")
@Schema(description = "门票统计表")
public class TicketStatisticsEntity {

    /**
     * 主键
     */
    @Schema(description="主键")
	@TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 门票详情id
     */
    @Schema(description="门票详情id")
    private Long ticketDetailId;
    /**
     * 门票id
     */
    @Schema(description="门票id")
    private Long ticketId;
	/**
	 * 参观日期
	 */
	@Schema(description="参观日期")
	private LocalDate visitDate;
    /**
     * 已售出票数（已核销+已过期）
     */
    @Schema(description="已售出票数（已核销+已过期）")
    private Integer soldQuantity;
    /**
     * 已使用票数（已核销）
     */
    @Schema(description="已使用票数（已核销）")
    private Integer usedQuantity;
    /**
     * 已过期票数（未核销且已过期）
     */
    @Schema(description="已过期票数（未核销且已过期）")
    private Integer expiredQuantity;
    /**
     * 退票数
     */
    @Schema(description="退票数")
    private Integer refundQuantity;
    /**
     * 创建日期
     */
    @Schema(description="创建日期")
    private Date createTime;

}

