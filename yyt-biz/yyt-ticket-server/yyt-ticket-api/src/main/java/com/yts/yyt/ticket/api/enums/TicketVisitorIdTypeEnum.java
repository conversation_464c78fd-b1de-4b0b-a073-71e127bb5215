package com.yts.yyt.ticket.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 游客证件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TicketVisitorIdTypeEnum {

    ID_CARD("00", "身份证"),
    PASSPORT("01", "护照"),
    
    ;

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TicketVisitorIdTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TicketVisitorIdTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

	public static boolean contains(String idType) {
        return Arrays.stream(values())
            .anyMatch(item -> item.getCode().equals(idType));
	}
}