package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理端门票统计查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端门票统计查询参数")
public class TravelTicketStatisticsQueryDTO extends BasePage {

    /**
     * 标题
     */
    @Schema(description="标题")
    private String title;
    /**
     * 开始时间
     */
    @Schema(description="开始时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @Schema(description="结束时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}