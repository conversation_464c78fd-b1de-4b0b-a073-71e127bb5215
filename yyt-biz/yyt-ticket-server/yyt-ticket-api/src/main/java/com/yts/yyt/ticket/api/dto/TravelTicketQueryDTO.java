package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "旅拍门票信息add DTO")
public class TravelTicketQueryDTO extends BasePage {

    /**
     * 标题
     */
    @Schema(description="标题")
    private String title;

    /**
     * 创建人
     */
    @Schema(description="创建人")
    private String createBy;

    /**
     * 开始时间
     */
    @Schema(description="开始时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @Schema(description="结束时间 yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}

