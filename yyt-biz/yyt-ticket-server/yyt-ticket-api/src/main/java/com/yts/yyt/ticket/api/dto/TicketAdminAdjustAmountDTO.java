package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 管理端调整订单金额请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端调整订单金额请求参数")
public class TicketAdminAdjustAmountDTO {

	/**
	 * 订单ID
	 */
	@NotNull(message = "订单ID不能为空")
	@Schema(description = "订单ID", example = "1000001")
	private Long orderId;

	/**
	 * 调整后金额
	 */
	@NotNull(message = "调整后金额不能为空")
	@PositiveOrZero(message = "调整后金额必须大于等于0")
	@Schema(description = "调整后金额", example = "88.00")
	private BigDecimal amount;
}