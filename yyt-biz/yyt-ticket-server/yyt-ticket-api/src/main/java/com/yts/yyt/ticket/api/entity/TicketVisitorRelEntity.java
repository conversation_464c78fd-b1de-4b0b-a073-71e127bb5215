package com.yts.yyt.ticket.api.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("ticket_visitor_rel")
@Schema(description = "票务订单与游客信息关联表")
public class TicketVisitorRelEntity extends Model<TicketVisitorRelEntity> {

	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	private Long ticketOrderId;

	private Long ticketVisitorId;

	@Schema(description="游客姓名")
	private String visitorName;

	@Schema(description="字典配置 证件类型")
	private String idType;

	@Schema(description="证件号码")
	private String idNumber;

	private LocalDateTime createTime;

}

