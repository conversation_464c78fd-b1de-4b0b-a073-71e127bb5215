package com.yts.yyt.ticket.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@TableName("travel_ticket_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "旅拍订单表")
public class TravelTicketOrderEntity  extends Model<TravelTicketOrderEntity>  {

    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 订单编号
     */
    @Schema(description="订单编号")
    private String orderNo;
    /**
     * 用户ID
     */
    @Schema(description="用户ID")
    private Long userId;
    /**
     * 用户昵称
     */
    @Schema(description="用户昵称")
    private String name;
    /**
     * 用户手机号
     */
    @Schema(description="用户手机号")
    private String mobile;
    /**
     * 门票ID
     */
    @Schema(description="门票ID")
    private Long ticketId;
    /**
     * 商品编号
     */
    @Schema(description="商品编号")
    private String goodsNo;
    /**
     * 下单时间
     */
    @Schema(description="下单时间")
    private LocalDateTime orderTime;
    /**
     * 支付时间
     */
    @Schema(description="支付时间")
    private LocalDateTime paymentTime;
    /**
     * 退款时间
     */
    @Schema(description="退款时间")
    private LocalDateTime refundTime;
    /**
     * 过期时间
     */
    @Schema(description="过期时间")
    private LocalDateTime expiredTime;
    /**
     * 核销时间
     */
    @Schema(description="核销时间")
    private LocalDateTime verificationTime;
    /**
     * 核销人手机号
     */
    @Schema(description="核销人手机号")
    private String verificationPhone;
    /**
     * 门票数量
     */
    @Schema(description="门票数量")
    private Integer ticketQuantity;
    /**
     * 门票单价
     */
    @Schema(description="门票单价")
    private BigDecimal unitPrice;
    /**
     * 订单总金额
     */
    @Schema(description="订单总金额")
    private BigDecimal totalAmount;
    /**
     * 应付金额
     */
    @Schema(description="应付金额")
    private BigDecimal payableAmount;
    /**
     * 退款金额
     */
    @Schema(description="退款金额")
    private BigDecimal refundAmount;
    /**
     * 退款类型(1-用户申请退款 2-系统过期退款)
     */
    @Schema(description="退款类型")
    private Integer refundType;
    /**
     * 订单状态(1-待支付 |2-已支付(待核销) |3-已使用（已核销） |4-已过期 |5-已关闭 |6-退款中 |7-已退款)
     */
    @Schema(description="订单状态(1-待支付 |2-已支付(待核销) |3-已使用（已核销） |4-已过期 |5-已关闭 |6-退款中 |7-已退款)")
    private Integer status;
    /**
     * 门票二维码图片url
     */
    @Schema(description="门票二维码图片url")
    private String qrCodeImg;
    /**
     * 门票码
     */
    @Schema(description="门票码")
    private String antiFakeCode;
    /**
     * 核销人用户id user_info表中id
     */
    @Schema(description="核销人用户id user_info表中id")
    private String verificationUserId;
    /**
     * 订单来源
     */
    @Schema(description="订单来源")
    private Integer orderSource;
    /**
     * 微信交易id
     */
    @Schema(description="微信交易id")
    private String transactionId;
    /**
     * 支付方式
     */
    @Schema(description="支付方式")
    private Integer payType;
    /**
     * 流水号
     */
    @Schema(description="流水号")
    private String paySerio;
    /**
     * 退款流水
     */
    @Schema(description="退款流水")
    private String refundSerio;
    /**
     * 订单类型. eg: ticket
     */
    @Schema(description="订单类型. eg: ticket")
    private String orderType;
    /**
     * 支付订单号 关联pay_trade_order:order_id
     */
    @Schema(description="支付订单号 关联pay_trade_order:order_id")
    private String payOrderId;
    /**
     * 创建时间
     */
    @Schema(description="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description="更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 删除标记(0-正常,1-已删除)
     */
    @Schema(description="删除标记(0-正常,1-已删除)")
    @TableLogic
    private Integer delFlag;
    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

}

