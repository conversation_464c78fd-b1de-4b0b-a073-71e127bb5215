package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 管理端启用禁用门票请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端启用禁用门票请求参数")
public class TicketAdminEditStatusDTO {

	/**
	 * 主键IDS
	 */
	@Schema(description="主键IDS")
	@NotEmpty(message = "ids不能为空")
	private List<Long> ids;

    /**
     * 状态
     */
    @Schema(description = "状态：0-启用 1-禁用", example = "0")
	@NotNull(message = "状态不能为空")
    private Integer status = 0;
} 