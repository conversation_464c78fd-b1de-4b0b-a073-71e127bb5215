package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TicketOrderPayCallbackDTO implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;
	/**
	 * 订单id
	 */
	private String orderId;

	/**
	 * 支付流水号
	 */
	private String paySerio;

	/**
	 * 支付状态
	 * @see com.yts.yyt.ticket.api.enums.OrderEnum.OrderPayStatus
	 */
	private Integer payStatus;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 支付订单id PayTradeOrder表中orderId字段
	 */
	private String payOrderId;

	/**
	 * 支付类型
	 */
	private String payType;

	@Schema(description = "微信交易id")
	private String outTransId;
    
    /**
     * 微信小程序openid
     */
    private String weAppOpenid;
}
