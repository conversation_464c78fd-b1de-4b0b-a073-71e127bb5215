package com.yts.yyt.ticket.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 管理端创建门票请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端创建门票请求参数")
public class TicketAdminTimeSlotDTO {

	/**
	 * 时段主键ID（有值为更新，无值为新增）
	 */
	@Schema(description = "时段主键ID（有值为更新，无值为新增）")
	private Long id;
	/**
	 * 时段开始时间
	 */
	@Schema(description="时段开始时间")
	private String startTime;
	/**
	 * 时段结束时间
	 */
	@Schema(description="时段结束时间")
	private String endTime;
	/**
	 * 时段结束时间
	 */
	@Schema(description="门票数")
	private Integer totalQuantity;

}