package com.yts.yyt.ticket.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.yts.yyt.common.alert.annotation.AlertException;
import com.yts.yyt.common.alert.enums.AlertLevel;
import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.file.core.FileProperties;
import com.yts.yyt.common.file.core.FileTemplate;
import com.yts.yyt.common.file.oss.service.OssTemplate;
import com.yts.yyt.common.pay.huifu.enums.HuiFuTradeTypeEnum;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.pay.api.enums.PayTypeEnum;
import com.yts.yyt.ticket.api.constant.Constant;
import com.yts.yyt.ticket.api.dto.TicketOrderPayCallbackDTO;
import com.yts.yyt.ticket.api.entity.TicketOrderEntity;
import com.yts.yyt.ticket.api.entity.TravelTicketOrderEntity;
import com.yts.yyt.ticket.api.enums.OrderEnum;
import com.yts.yyt.ticket.api.enums.TicketOrderStatusEnum;
import com.yts.yyt.ticket.api.exception.TicketErrorEnum;
import com.yts.yyt.ticket.api.exception.TicketException;
import com.yts.yyt.ticket.api.vo.TicketPayParameterVO;
import com.yts.yyt.ticket.mq.dto.WxOrderShippingMqDTO;
import com.yts.yyt.ticket.service.BaseRemoteService;
import com.yts.yyt.ticket.service.TicketOrderPayCallbackService;
import com.yts.yyt.ticket.service.TicketOrderService;
import com.yts.yyt.ticket.service.TravelTicketOrderService;
import com.yts.yyt.ticket.util.QRCodeGenerator;
import com.yts.yyt.user.api.vo.UserInfoVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
@AllArgsConstructor
public class TicketOrderPayCallbackServiceImpl implements TicketOrderPayCallbackService {

    private final TicketOrderService ticketOrderService;
    private final TravelTicketOrderService travelTicketOrderService;
    private final QRCodeGenerator qrCodeGenerator;
    private final FileTemplate fileTemplate;
    private final FileProperties properties;
    private final BaseRemoteService baseRemoteService;
    private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;

    @Lock4j(keys = "#dto.orderId", expire = 3000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    @AlertException(value = "订单支付回调", modules = ServiceNameConstants.TICKET_SERVER, level = AlertLevel.CRITICAL)
    public Boolean payCallback(TicketOrderPayCallbackDTO dto) {
        log.info("收到订单支付回调：{}", dto);
        validateCallbackParams(dto);
        Long orderId = Long.parseLong(dto.getOrderId());

        // 检查支付状态，只处理支付成功的回调
        if (OrderEnum.OrderPayStatus.SUCCESS.getType() != dto.getPayStatus()) {
            log.info("票务服务 支付回调处理：支付未成功，订单号：{}，支付状态：{}", dto.getOrderId(), dto.getPayStatus());
            return true; // 非支付成功的状态，直接返回成功，不进行后续处理
        }

        // 先尝试查询普通门票订单
        TicketOrderEntity ticketOrder = ticketOrderService.getById(orderId);
        if (ticketOrder != null) {
            return handleTicketPayCallback(ticketOrder, dto);
        }
        
        // 再尝试查询旅拍订单
        TravelTicketOrderEntity travelOrder = travelTicketOrderService.getById(orderId);
        if (travelOrder != null) {
            return handleTravelTicketPayCallback(travelOrder, dto);
        }
        
        log.error("支付回调处理失败：订单不存在，订单号：{}", dto.getOrderId());
        throw TicketException.build(TicketErrorEnum.ORDER_NOT_EXIST);
    }

    @Override
    @AlertException(value = "获取支付参数", modules = ServiceNameConstants.TICKET_SERVER, level = AlertLevel.CRITICAL)
    public TicketPayParameterVO getPayParams(Long orderId, String openId) {
        log.info("获取订单支付参数，订单ID：{}", orderId);
        if (orderId == null) {
            log.error("获取支付参数失败：订单ID为空");
            throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
        }

        // 先尝试查询普通门票订单
        TicketOrderEntity ticketOrder = ticketOrderService.getById(orderId);
        if (ticketOrder != null) {
            return getTicketOrderPayParams(ticketOrder, openId);
        }

        // 再尝试查询旅拍订单
        TravelTicketOrderEntity travelOrder = travelTicketOrderService.getById(orderId);
        if (travelOrder != null) {
            return getTravelTicketOrderPayParams(travelOrder, openId);
        }

        log.error("获取支付参数失败：订单不存在，订单ID：{}", orderId);
        throw TicketException.build(TicketErrorEnum.ORDER_NOT_EXIST);
    }

    /**
     * 处理普通门票支付回调
     */
    private Boolean handleTicketPayCallback(TicketOrderEntity order, TicketOrderPayCallbackDTO dto) {
        if (!TicketOrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            if (TicketOrderStatusEnum.PAID_UNVERIFIED.getCode().equals(order.getStatus())) {
                log.info("门票支付回调重复处理，订单号：{}，当前状态：已支付", order.getOrderNo());
                return true;
            }
            log.error("门票支付回调订单状态异常，订单号：{}，当前状态：{}", order.getOrderNo(), order.getStatus());
            throw TicketException.build(TicketErrorEnum.ORDER_STATUS_ERROR);
        }

        setOrderPayInfo(order, dto);
        updateOrderPayStatus(order.getId(), () -> ticketOrderService.updateById(order));
        generateTicketQRCode(order);
        sendShippingNotification(order.getId(), order.getOrderNo(), order.getTransactionId(), 
                                 Constant.TICKET_GOOD_NAME, "TICKET",order.getUserId());
        log.info("门票订单支付回调处理成功，订单号：{}", order.getOrderNo());
        return true;
    }

    /**
     * 处理旅拍票支付回调
     */
    private Boolean handleTravelTicketPayCallback(TravelTicketOrderEntity order, TicketOrderPayCallbackDTO dto) {
        if (!TicketOrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            if (TicketOrderStatusEnum.PAID_UNVERIFIED.getCode().equals(order.getStatus())) {
                log.info("旅拍支付回调重复处理，订单号：{}，当前状态：已支付", order.getOrderNo());
                return true;
            }
            log.error("旅拍支付回调订单状态异常，订单号：{}，当前状态：{}", order.getOrderNo(), order.getStatus());
            throw TicketException.build(TicketErrorEnum.ORDER_STATUS_ERROR);
        }

        setOrderPayInfo(order, dto);
        updateOrderPayStatus(order.getId(), () -> travelTicketOrderService.updateById(order));
        generateTravelTicketQRCode(order);
        sendShippingNotification(order.getId(), order.getOrderNo(), order.getTransactionId(), 
                                 Constant.TRAVEL_TICKET_GOOD_NAME, "TRAVEL_TICKET", order.getUserId());
        log.info("旅拍订单支付回调处理成功，订单号：{}", order.getOrderNo());
        return true;
    }

    /**
     * 获取普通门票支付参数
     */
    private TicketPayParameterVO getTicketOrderPayParams(TicketOrderEntity order, String openId) {
        if (!TicketOrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            log.error("获取支付参数失败：门票订单状态异常，订单ID：{}，状态：{}", order.getId(), order.getStatus());
            throw TicketException.build(TicketErrorEnum.ORDER_STATUS_ERROR);
        }

        return buildPayParams(order.getId(), order.getOrderNo(), order.getPayableAmount(), 
                             order.getExpiredTime(), order.getGoodsNo(), order.getUserId(), 
                             Constant.TICKET_GOOD_NAME,PayTypeEnum.TICKET_ORDER_PAYBACK.getCode());
    }

    /**
     * 获取旅拍票支付参数
     */
    private TicketPayParameterVO getTravelTicketOrderPayParams(TravelTicketOrderEntity order, String openId) {
        if (!TicketOrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            log.error("获取支付参数失败：旅拍订单状态异常，订单ID：{}，状态：{}", order.getId(), order.getStatus());
            throw TicketException.build(TicketErrorEnum.ORDER_STATUS_ERROR);
        }

        return buildPayParams(order.getId(), order.getOrderNo(), order.getPayableAmount(), 
                             order.getExpiredTime(), order.getGoodsNo(), order.getUserId(), 
                             Constant.TRAVEL_TICKET_GOOD_NAME,PayTypeEnum.TRAVEL_TICKET_ORDER_PAYBACK.getCode());
    }

    /**
     * 设置订单支付信息（通用方法 - 反射优化版）
     */
    private void setOrderPayInfo(Object order, TicketOrderPayCallbackDTO dto) {
        try {
            Class<?> orderClass = order.getClass();
            
            // 通过反射设置通用字段
            setFieldValue(order, "status", TicketOrderStatusEnum.PAID_UNVERIFIED.getCode());
            setFieldValue(order, "paymentTime", LocalDateTime.now());
            setFieldValue(order, "payType", getPayTypeFromPayType(dto.getPayType()));
            setFieldValue(order, "transactionId", dto.getOutTransId());
            setFieldValue(order, "paySerio", dto.getPaySerio());
            setFieldValue(order, "payOrderId", dto.getPayOrderId());
            setFieldValue(order, "updateTime", LocalDateTime.now());
            
            log.debug("订单支付信息设置成功，订单类型：{}", orderClass.getSimpleName());
        } catch (Exception e) {
            log.error("设置订单支付信息失败，订单类型：{}，错误：{}", order.getClass().getSimpleName(), e.getMessage(), e);
            throw TicketException.build(TicketErrorEnum.SYSTEM_ERROR);
        }
    }
    
    /**
     * 反射设置字段值
     */
    private void setFieldValue(Object target, String fieldName, Object value) throws Exception {
        Class<?> clazz = target.getClass();
        
        // 构造setter方法名
        String setterName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        
        // 查找setter方法
        Method setter = null;
        for (Method method : clazz.getMethods()) {
            if (method.getName().equals(setterName) && method.getParameterCount() == 1) {
                setter = method;
                break;
            }
        }
        
        if (setter != null) {
            setter.invoke(target, value);
        } else {
            log.warn("未找到setter方法：{}，类：{}", setterName, clazz.getSimpleName());
        }
    }

    /**
     * 统一更新订单支付状态
     */
    private void updateOrderPayStatus(Long orderId, java.util.function.Supplier<Boolean> updateFunction) {
        boolean updateResult = updateFunction.get();
        if (!updateResult) {
            log.error("更新订单支付状态失败，订单ID：{}", orderId);
            throw TicketException.build(TicketErrorEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 统一构建支付参数
     */
    private TicketPayParameterVO buildPayParams(Long orderId, String orderNo, BigDecimal amount,
                                               LocalDateTime expiredTime, String goodsNo, 
                                               Long userId, String goodsName,String orderType) {
        TicketPayParameterVO payParams = new TicketPayParameterVO();
        payParams.setOrderId(orderId);
        payParams.setOrderNo(orderNo);
        payParams.setAmount(amount);
        payParams.setDelayAcctFlag("N");
        payParams.setAcctSplitBunchFlag("N");
        payParams.setOrderType(orderType);
        payParams.setExpirseTime(expiredTime);
        payParams.setGoodsNo(goodsNo);
        payParams.setGoodsName(goodsName);

        UserInfoVO userInfo = baseRemoteService.getUserInfo(userId);
        payParams.setWxSubOpenid(userInfo.getWeappOpenid());
        payParams.setUserId(userId);

        log.info("获取订单支付参数成功，订单ID：{}", orderId);
        return payParams;
    }

    /**
     * 生成门票二维码
     */
    private void generateTicketQRCode(TicketOrderEntity order) {
        generateQRCodeAndSecurityCode(order.getId(), order.getOrderNo(), "ticket", 
                                      code -> ticketOrderService.lambdaQuery()
                                              .eq(TicketOrderEntity::getAntiFakeCode, code)
                                              .exists(),
                                      (qrUrl, securityCode) -> {
                                          order.setQrCodeImg(qrUrl);
                                          order.setAntiFakeCode(securityCode);
                                      });
        ticketOrderService.updateById(order);
    }

    /**
     * 生成旅拍票二维码
     */
    private void generateTravelTicketQRCode(TravelTicketOrderEntity order) {
        generateQRCodeAndSecurityCode(order.getId(), order.getOrderNo(), "travel_ticket",
                                      code -> travelTicketOrderService.lambdaQuery()
                                              .eq(TravelTicketOrderEntity::getAntiFakeCode, code)
                                              .exists(),
                                      (qrUrl, securityCode) -> {
                                          order.setQrCodeImg(qrUrl);
                                          order.setAntiFakeCode(securityCode);
                                      });
        travelTicketOrderService.updateById(order);
    }

    /**
     * 通用二维码和防伪码生成方法
     */
    private void generateQRCodeAndSecurityCode(Long orderId, String orderNo, String folderPrefix,
                                               java.util.function.Function<String, Boolean> codeExistsChecker,
                                               java.util.function.BiConsumer<String, String> resultHandler) {
        try {
            // 1. 生成二维码，内容仅包含订单ID
            String qrCodeContent = orderId.toString();
            BufferedImage qrImage = qrCodeGenerator.generateHighQualityQRCode(qrCodeContent, null);

            // 2. 将二维码图片上传到OSS
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(qrImage, "PNG", outputStream);
            byte[] qrCodeBytes = outputStream.toByteArray();

            // 获取当前年月日作为文件夹路径
            LocalDateTime now = LocalDateTime.now();
            String yearMonthDay = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = folderPrefix + "/" + yearMonthDay + "/" + IDS.uniqueID() + ".jpg";

            // 上传文件
            fileTemplate.putObject(properties.getBucketName(), fileName,
                    new ByteArrayInputStream(qrCodeBytes), "image/jpeg");

            // 获取图片URL
            String qrCodeUrl = "";
            if (fileTemplate instanceof OssTemplate) {
                qrCodeUrl = ((OssTemplate) fileTemplate).getObjectURL(properties.getBucketName(), fileName, 7);
                // 截取基础URL，去掉参数部分
                if (qrCodeUrl.contains("?")) {
                    qrCodeUrl = qrCodeUrl.split("\\?")[0];
                }
            }

            // 3. 生成防伪码：前6位为年月日，后6位是随机的
            String securityCodePrefix = now.format(DateTimeFormatter.ofPattern("yyMMdd")); // 年月日前缀
            String securityCode = generateUniqueSecurityCode(securityCodePrefix, codeExistsChecker);

            // 4. 回调设置结果
            resultHandler.accept(qrCodeUrl, securityCode);

            log.info("订单二维码和防伪码生成成功，类型：{}，订单ID：{}，订单号：{}，二维码URL：{}，防伪码：{}", folderPrefix, orderId, orderNo,qrCodeUrl,securityCode);
        } catch (Exception e) {
            log.error("订单二维码和防伪码生成失败，类型：{}，订单ID：{}，订单号：{}，错误：{}", folderPrefix, orderId, orderNo, e.getMessage(), e);
            throw TicketException.build(TicketErrorEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 统一发送发货通知
     */
    private void sendShippingNotification(Long orderId, String orderNo, String transactionId,
                                          String itemDesc, String orderType, Long userId) {
            UserInfoVO userInfo = baseRemoteService.getUserInfo(userId);
            WxOrderShippingMqDTO mqDTO = new WxOrderShippingMqDTO();
            mqDTO.setBusinessId(orderId);
            mqDTO.setOrderNumberType(2); // 微信支付单号形式
            mqDTO.setTransactionId(transactionId);
            mqDTO.setLogisticsType(3); // 虚拟商品
            mqDTO.setDeliveryMode(1); // 统一发货
            mqDTO.setItemDesc(itemDesc);
            mqDTO.setOpenid(userInfo.getWeappOpenid());
            
            Message<WxOrderShippingMqDTO> message = MessageBuilder.withPayload(mqDTO)
                    .setHeader("orderId", orderId)
                    .setHeader("orderType", orderType)
                    .build();
        try {
            envAwareRocketMQTemplate.convertAndSend(RocketMQConstants.Topic.TOPIC_ORDER_SHIPPING, message);
            log.info("{}发货通知发送成功，订单号：{}", itemDesc, orderNo);
        } catch (Exception e) {
            log.error("{}发货通知发送失败，订单号：{}，错误：{}", itemDesc, orderNo, e.getMessage(), e);
            // 发货通知失败不影响支付回调的主流程，仅记录日志
        }
    }

    /**
     * 参数校验
     */
    private void validateCallbackParams(TicketOrderPayCallbackDTO dto) {
        if (!StringUtils.hasText(dto.getOrderId())) {
            log.error("支付回调参数校验失败：订单ID为空");
            throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
        }
        
        if (!StringUtils.hasText(dto.getPaySerio())) {
            log.error("支付回调参数校验失败：支付流水号为空");
            throw TicketException.build(TicketErrorEnum.PARAM_ERROR);
        }
    }

    /**
     * 根据支付类型获取支付方式
     */
    private Integer getPayTypeFromPayType(String payType) {
        if (payType == null) {
            return OrderEnum.OrderPayType.PAYMENT_COMPANY_PAY.getType();
        }
        
        if (HuiFuTradeTypeEnum.T_MINIAPP.getType().equals(payType)) {
            return OrderEnum.OrderPayType.MPWECHAT_PAY.getType();
        } else if (HuiFuTradeTypeEnum.A_JSAPI.getType().equals(payType)) {
            return OrderEnum.OrderPayType.ALI_PAY.getType();
        }
        return OrderEnum.OrderPayType.PAYMENT_COMPANY_PAY.getType();
    }

    /**
     * 生成唯一的防伪码
     * 防伪码格式: 前缀(6位年月日) + 6位随机数字
     *
     * @param prefix 年月日前缀
     * @param codeExistsChecker 检查防伪码是否存在的函数
     * @return 12位唯一防伪码
     */
    private String generateUniqueSecurityCode(String prefix, java.util.function.Function<String, Boolean> codeExistsChecker) {
        String securityCode;
        int retryCount = 0;

        do {
            // 生成6位随机数字
            String randomCode = RandomStringUtils.randomNumeric(6);
            securityCode = prefix + randomCode;

            // 检查防伪码是否存在
            if (!codeExistsChecker.apply(securityCode)) {
                return securityCode;
            }

            retryCount++;
            // 最多尝试10次生成唯一防伪码
        } while (retryCount < 10);

        // 如果10次都无法生成唯一防伪码，使用时间戳后6位
        String timestamp = String.valueOf(System.currentTimeMillis());
        return prefix + timestamp.substring(timestamp.length() - 6);
    }
}
