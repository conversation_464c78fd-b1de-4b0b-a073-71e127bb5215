package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 拍品分销启/停申请提交DTO
 *
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销启/停申请提交DTO")
public class DistLotApplyAddDTO {

    /**
     * 拍品 lot 主键（goods_lot_info.id）
     */
    @NotNull(message = "拍品ID不能为空")
    @Schema(description = "拍品 lot 主键（goods_lot_info.id）")
    private Long lotId;

    /**
     * 取消分销=cancel 恢复分销=recovery
     */
    @NotBlank(message = "申请动作不能为空")
    @Schema(description = "取消分销=cancel 恢复分销=recovery")
    private String action;
}