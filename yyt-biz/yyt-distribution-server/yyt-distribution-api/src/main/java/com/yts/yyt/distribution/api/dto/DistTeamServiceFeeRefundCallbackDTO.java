package com.yts.yyt.distribution.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DistTeamServiceFeeRefundCallbackDTO implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 支付流水号
         */
        private String paySerio;
        /**
         * 订单id
         */
        private String orderId;

        /**
         * 订单类型
         */
        private String orderType;

        /**
         * 交易时间
         */
        private LocalDateTime tradeTime;
}
