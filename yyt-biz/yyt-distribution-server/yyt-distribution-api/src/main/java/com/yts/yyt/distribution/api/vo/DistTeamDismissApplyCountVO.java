package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 团队解散申请数量统计VO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "团队解散申请数量统计VO")
public class DistTeamDismissApplyCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审批通过数量
     */
    @Schema(description = "审批通过数量")
    private Long approveCount;

    /**
     * 审批拒绝数量
     */
    @Schema(description = "审批拒绝数量")
    private Long rejectCount;

    /**
     * 待审批数量
     */
    @Schema(description = "待审批数量")
    private Long pendingCount;

    /**
     * 全部数量
     */
    @Schema(description = "全部数量")
    private Long totalCount;
} 