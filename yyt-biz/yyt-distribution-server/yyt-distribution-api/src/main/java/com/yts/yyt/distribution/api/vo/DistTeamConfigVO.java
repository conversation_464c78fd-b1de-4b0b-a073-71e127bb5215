package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "分销配置表-vo")
public class DistTeamConfigVO {

    /**
     * 主键 ID（雪花ID）
     */
    @Schema(description="主键 ID（雪花ID）")
    private Long id;
    /**
     * 成为团长需缴服务费金额（元）
     */
    @Schema(description="成为团长需缴服务费金额（元）")
    private BigDecimal leaderServiceFeeAmt;
    /**
     * 服务费有效期：1=长期 2=1年 …
     */
    @Schema(description="服务费有效期：1=长期 2=1年 …")
    private Integer leaderServiceFeeTerm;
    /**
     * 最低可提现金额（元）
     */
    @Schema(description="最低可提现金额（元）")
    private BigDecimal minWithdrawAmt;

}

