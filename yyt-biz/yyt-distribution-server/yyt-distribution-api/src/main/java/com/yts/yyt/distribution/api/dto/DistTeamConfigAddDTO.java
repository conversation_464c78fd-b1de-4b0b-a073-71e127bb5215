package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "分销团队配置表-add")
public class DistTeamConfigAddDTO {

    /**
     * 主键 ID（雪花ID）
     */
    @Schema(description="主键 ID（雪花ID）")
    private Long id;
    /**
     * 成为团长需缴服务费金额（元）
     */
    @Schema(description="成为团长需缴服务费金额（元）")
    @NotNull(message = "成为团长需缴服务费金额（元）不能为空")
    private BigDecimal leaderServiceFeeAmt;
    /**
     * 服务费有效期：1=长期 2=1年 …
     */
    @Schema(description="服务费有效期：1=长期 2=1年 …")
    @NotNull(message = "服务费有效期不能为空")
    private Integer leaderServiceFeeTerm;
    /**
     * 最低可提现金额（元）
     */
    @Schema(description="最低可提现金额（元）")
    @NotNull(message = "最低可提现金额不能为空")
    private BigDecimal minWithdrawAmt;
}

