package com.yts.yyt.distribution.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 拍品分销管理导出VO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销管理导出VO")
public class DistLotInfoExportVO {

    /**
     * 拍品名称
     */
    @ExcelProperty("拍品名称")
    @Schema(description = "拍品名称")
    private String lotName;

    /**
     * 拍品编码 / 编号
     */
    @ExcelProperty("拍品编码")
    @Schema(description = "拍品编码 / 编号")
    private String lotCode;

    /**
     * 商家名称
     */
    @ExcelProperty("商家名称")
    @Schema(description = "商家名称")
    private String merchantName;

    /**
     * 销售价格
     */
    @ExcelProperty("销售价格")
    @Schema(description = "销售价格")
    private BigDecimal salePrice;

    /**
     * 链接查看数
     */
    @ExcelProperty("链接查看数")
    @Schema(description = "链接查看数")
    private Integer linkViewCnt;
}