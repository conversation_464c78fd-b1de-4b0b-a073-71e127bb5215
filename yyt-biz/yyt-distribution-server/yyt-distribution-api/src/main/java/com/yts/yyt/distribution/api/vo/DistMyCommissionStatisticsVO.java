package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class DistMyCommissionStatisticsVO {

    @Schema(description = "本月佣金收入")
    private BigDecimal incomeAmt;
    @Schema(description = "本月佣金扣减")
    private BigDecimal deductionAmt;
    /**
     * @see com.yts.yyt.distribution.api.enums.DistOrderCommissionStatusEnum
     */
    @Schema(description = "状态：normal-有效 invalid-失效")
    private String status;

}