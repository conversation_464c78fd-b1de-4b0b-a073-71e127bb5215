package com.yts.yyt.distribution.api.vo;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 团队基本信息VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Schema(description = "团队基本信息VO")
public class DistTeamSimpleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 团队ID
     */
    @Schema(description = "团队ID")
    private Long id;

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    private String teamName;
} 