package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 拍品分销管理分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "拍品分销管理分页查询DTO")
public class DistLotInfoPageDTO extends BasePage {

    /**
     * 拍品名称/ID
     */
    @Schema(description = "拍品名称/ID")
    private String lotKeyword;

    /**
     * 商家名称/ID
     */
    @Schema(description = "商家名称/ID")
    private String merchantKeyword;

    /**
     * 状态筛选：all-全部 not_shared-未分享 shared-已分享 not_dist-非分销 pending-待审批
     */
    @Schema(description = "状态筛选：all-全部 not_shared-未分享 shared-已分享 not_dist-非分销 pending-待审批")
    private String status;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间")
    private LocalDateTime startTime;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间")
    private LocalDateTime endTime;
    @Schema(description = "商家ids",hidden = true)
    private List<Long> merchantIds;
} 