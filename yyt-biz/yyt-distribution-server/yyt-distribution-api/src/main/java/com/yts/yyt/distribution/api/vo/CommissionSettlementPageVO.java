package com.yts.yyt.distribution.api.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业绩结算分页查询结果VO
 *
 * @since 2025-01-27
 */
@Data
@Schema(description = "业绩结算分页查询结果VO")
public class CommissionSettlementPageVO {

    /**
     * 分销员ID
     */
    @Schema(description = "分销员ID")
    @ExcelIgnore
    private Long distributorId;

    /**
     * 分销员名称
     */
    @ExcelProperty(value = "分销员", index = 0)
    @Schema(description = "分销员名称")
    private String distributorName;

    /**
     * 团长/成员等级
     */
    @ExcelProperty(value = "甄选官/成员等级", index = 1)
    @Schema(description = "甄选官/成员等级")
    private String levelName;

    /**
     * 分享佣金
     */
    @ExcelProperty(value = "结算佣金", index = 2)
    @Schema(description = "结算佣金")
    private BigDecimal shareCommission;

    /**
     * 是否团长：0=否 1=是
     */
    @Schema(description = "是否团长：0=否 1=是")
    @ExcelProperty(value = "是否甄选官", index = 3, converter = CommissionSettlementPageVO.IsTeamLeaderConverter.class)
    private Integer isTeamLeader;

    /**
     * 团长激励佣金
     */
    @ExcelProperty(value = "甄选官激励佣金", index = 4)
    @Schema(description = "甄选官激励佣金")
    private BigDecimal teamLeaderIncentiveCommission;

    /**
     * 待结算佣金
     */
    @ExcelProperty(value = "待结算佣金", index = 5)
    @Schema(description = "待结算佣金")
    private BigDecimal pendingCommission;

    /**
     * 可提现佣金
     */
    @ExcelProperty(value = "可提现佣金", index = 6)
    @Schema(description = "可提现佣金")
    private BigDecimal withdrawableCommission;

    /**
     * 已提现佣金
     */
    @ExcelProperty(value = "已提现佣金", index = 7)
    @Schema(description = "已提现佣金")
    private BigDecimal withdrawnCommission;

    /**
     * 交易数量
     */
    @ExcelProperty(value = "交易数量", index = 8)
    @Schema(description = "交易数量")
    private Integer transactionCount;

    /**
     * 自定义转换器：将0/1转换为"否"/"是"
     */
    public static class IsTeamLeaderConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) {
            Integer value = context.getValue();
            String result = (value != null && value == 1) ? "是" : "否";
            return new WriteCellData<>(result);
        }

        @Override
        public Integer convertToJavaData(ReadConverterContext<?> context) {
            String cellValue = context.getReadCellData().getStringValue();
            return "是".equals(cellValue) ? 1 : 0;
        }
    }
} 