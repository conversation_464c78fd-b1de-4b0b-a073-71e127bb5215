package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 拍品分销启/停申请分页查询DTO
 *
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "拍品分销启/停申请分页查询DTO")
public class DistLotApplyPageDTO extends BasePage {

    /**
     * 拍品名称或ID
     */
    @Schema(description = "拍品名称或ID")
    private String lotKeyword;

    /**
     * 商家名称或ID
     */
    @Schema(description = "商家名称或ID")
    private String merchantKeyword;

    /**
     * 拍品分销状态：未分享=not_shared 已分享=shared 未分销=not_distributed 待审批=pending
     */
    @Schema(description = "拍品分销状态：未分享=not_shared 已分享=shared 未分销=not_distributed 待审批=pending")
    private String status;
}