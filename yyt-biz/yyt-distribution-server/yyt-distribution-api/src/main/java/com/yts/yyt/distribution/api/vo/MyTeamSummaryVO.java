package com.yts.yyt.distribution.api.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 我的团队汇总VO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "我的团队汇总VO")
public class MyTeamSummaryVO {
    /**
     * 团队成员数量
     */
    @Schema(description = "团队成员数量")
    private Integer memberCount;

    /**
     * 团队GMV
     */
    @Schema(description = "团队GMV")
    private BigDecimal teamGmv;

    /**
     * 团长服务费
     */
    @Schema(description = "团长服务费")
    private BigDecimal serviceFee;

    /**
     * 团队状态
     */
    @Schema(description = "团队状态")
    private Integer status;

	/**
	 * 团队名称
	 */
	@Schema(description="团队名称")
	private String teamName;
} 
