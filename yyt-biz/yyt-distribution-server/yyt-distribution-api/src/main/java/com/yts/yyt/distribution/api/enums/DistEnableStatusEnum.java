package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分销状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum DistEnableStatusEnum {

    /**
     * 非分销状态
     */
    NOT_DIST(0, "非分销"),

    /**
     * 分销中
     */
    DIST_ENABLED(1, "分销中");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举对象，未找到返回null
     */
    public static DistEnableStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (DistEnableStatusEnum value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断状态值是否有效
     *
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        return getByStatus(status) != null;
    }

    /**
     * 是否为分销中状态
     *
     * @param status 状态值
     * @return 是否为分销中
     */
    public static boolean isDistEnabled(Integer status) {
        return DIST_ENABLED.getStatus().equals(status);
    }

    /**
     * 是否为非分销状态
     *
     * @param status 状态值
     * @return 是否为非分销
     */
    public static boolean isNotDist(Integer status) {
        return NOT_DIST.getStatus().equals(status);
    }
} 