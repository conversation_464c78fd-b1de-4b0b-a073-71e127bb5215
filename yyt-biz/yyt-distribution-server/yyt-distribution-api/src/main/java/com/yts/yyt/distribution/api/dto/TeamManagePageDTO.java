package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TeamManagePageDTO extends BasePage {
    @Schema(description = "关键词（团名/团长名称/电话）")
    private String keyword;

    @Schema(description = "团长等级")
    private String leaderCode;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "组团开始时间")
    private String startTime;

    @Schema(description = "组团结束时间")
    private String endTime;
} 
