package com.yts.yyt.distribution.api.vo;

import com.yts.yyt.distribution.api.constants.TeamServiceFeeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DistTeamServiceFeeDetailVO {
    @Schema(description = "流水号")
    private String paySerio;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "支付状态 CANCEL-已取消  PENDING - 待支付  PROCESSING - 支付中 BEEN_PAY-已支付 FAIL-支付失败")
    private String payStatus;

    @Schema(description = "支付渠道" ,implementation = TeamServiceFeeEnum.RetStatusEnum.class)
    private String payChannel;
}