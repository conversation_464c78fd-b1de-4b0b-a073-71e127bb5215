package com.yts.yyt.distribution.api.enums;

import lombok.Getter;

/**
 * 团队成员操作类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum TeamMemberOperationEnum {

    /**
     * 加入团队
     */
    JOIN(1, "加入团队"),

    /**
     * 离开团队
     */
    LEAVE(2, "离开团队");

    /**
     * 操作类型编码
     */
    private final Integer code;

    /**
     * 操作类型描述
     */
    private final String desc;

    TeamMemberOperationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TeamMemberOperationEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TeamMemberOperationEnum item : values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }
        return null;
    }
} 