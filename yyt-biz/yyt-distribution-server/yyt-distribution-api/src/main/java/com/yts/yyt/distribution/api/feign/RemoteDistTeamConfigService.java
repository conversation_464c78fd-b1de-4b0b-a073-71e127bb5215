package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.vo.DistTeamConfigVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 分销团队配置 远程服务接口
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@FeignClient(contextId = "remoteDistTeamConfigService", value = ServiceNameConstants.DISTRIBUTION_SERVER, path = "/distTeamConfig")
public interface RemoteDistTeamConfigService {

    /**
     * feign 团长服务费/最低提现金额-查询
     *
     * @return list数据
     */
    @NoToken
    @PostMapping("/feign/queryConfig")
    R<DistTeamConfigVO> feignQueryDistTeamConfig();

}
