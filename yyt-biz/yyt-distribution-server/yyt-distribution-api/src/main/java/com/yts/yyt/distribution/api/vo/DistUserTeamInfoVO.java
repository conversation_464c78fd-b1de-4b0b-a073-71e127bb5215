package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户团队信息VO")
public class DistUserTeamInfoVO {
    @Schema(description = "用户分销角色(leader-团长，member-团员，空字符串-无角色)")
    private String role;
    @Schema(description = "团队ID")
    private Long teamId;
    @Schema(description = "团队名称")
    private String teamName;
    @Schema(description = "团队状态(1-正常，2-禁用，3-解散)")
    private Integer teamStatus;
}