package com.yts.yyt.distribution.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "分销订单分页查询VO")
public class DistOrderPageVO {

    /**
     * 订单编号
     */
    @Schema(description="订单编号")
    @ExcelProperty("订单编号")
    @ColumnWidth(20)
    private String orderNo;
    /**
     * 拍品名称
     */
    @Schema(description="拍品名称")
    @ExcelProperty("拍品名称")
    @ColumnWidth(30)
    private String lotName;
    /**
     * 成交金额(元)
     */
    @Schema(description="成交金额(元)")
    @ExcelProperty("成交金额")
    @ColumnWidth(15)
    private BigDecimal amount;
    /**
     * 分销员用户 ID
     */
    @Schema(description="分销员用户 ID")
    private Long sharerId;
    /**
     * 分销员
     */
    @Schema(description="分销员")
    @ExcelProperty("分享人")
    @ColumnWidth(15)
    private String sharerName;
    /**
     * 团长id
     */
    @Schema(description="团长id")
    private Long teamLeaderId;
    /**
     * 团长
     */
    @Schema(description="团长")
    @ExcelProperty("团长")
    @ColumnWidth(15)
    private String teamLeader;
    /**
     * 买家用户 ID
     */
    @Schema(description="买家用户 ID")
    private Long buyerId;
    /**
     * 买家
     */
    @Schema(description="买家")
    @ExcelProperty("买家")
    @ColumnWidth(15)
    private String buyerName;
    /**
     * 买家联系电话
     */
    @Schema(description="买家联系电话")
    @ExcelProperty("买家联系电话")
    @ColumnWidth(15)
    private String buyerPhone;
    /**
     * 商家名称
     */
    @Schema(description="商家名称")
    @ExcelProperty("商家名称")
    @ColumnWidth(20)
    private String shopName;
    /**
     * 订单来源
     */
    @Schema(description="订单来源")
    @ExcelProperty("订单来源")
    @ColumnWidth(15)
    private String orderSource;
    /**
     * 结算方式
     */
    @Schema(description="结算方式")
    @ExcelProperty("结算方式")
    @ColumnWidth(15)
    private String payType;

    /**
     * 订单状态：0已支付 1已发货 2已签收 3已完成 4已退款
     */
    @Schema(description="订单状态：0已支付 1已发货 2已签收 3已完成 4已退款")
    private Integer status;

    /**
     * 订单状态：0已支付 1已发货 2已签收 3已完成 4已退款
     */
    @Schema(description="订单状态：0已支付 1已发货 2已签收 3已完成 4已退款")
    @ExcelProperty("订单状态")
    @ColumnWidth(15)
    private String statusName;

    @Schema(description="创建时间")
    @ExcelProperty("下单时间")
    @ColumnWidth(20)
    private LocalDateTime payTime;

}

