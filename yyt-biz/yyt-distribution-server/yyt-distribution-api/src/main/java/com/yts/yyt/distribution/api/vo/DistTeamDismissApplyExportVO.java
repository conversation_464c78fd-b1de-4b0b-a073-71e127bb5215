package com.yts.yyt.distribution.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DistTeamDismissApplyExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("团名")
    private String teamName;

    @ExcelProperty("甄选官名称")
    private String leaderName;

    @ExcelProperty("甄选官等级")
    private String levelName;

    @ExcelProperty("联系手机")
    private String mobile;

    @ExcelProperty("团队GMV")
    private BigDecimal teamGmv;

    @ExcelProperty("甄选官服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("提交时间")
    private LocalDateTime applyTime;

    @ExcelProperty("状态")
    private String status;
}
