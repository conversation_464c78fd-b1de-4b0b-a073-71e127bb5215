package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DistMyGmvVO {
    @Schema(description = "拍品图片")
    private String lotImage;

    @Schema(description = "拍品名称")
    private String lotName;

    @Schema(description = "拍品售价")
    private BigDecimal lotPrice;

    @Schema(description = "下单时间")
    private LocalDateTime payTime;

    @Schema(description = "分享人佣金（我的佣金）")
    private BigDecimal sharerCommission;

}