package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 业绩结算合计数据VO
 *
 * @since 2025-01-27
 */
@Data
@Schema(description = "业绩结算合计数据VO")
public class CommissionSettlementSummaryVO {

    /**
     * 分享佣金总额
     */
    @Schema(description = "分享佣金总额")
    private BigDecimal totalShareCommission;

    /**
     * 待结算佣金总额
     */
    @Schema(description = "待结算佣金总额")
    private BigDecimal totalPendingCommission;

    /**
     * 待提现佣金总额
     */
    @Schema(description = "待提现佣金总额")
    private BigDecimal totalWithdrawableCommission;

    /**
     * 已提现佣金总额
     */
    @Schema(description = "已提现佣金总额")
    private BigDecimal totalWithdrawnCommission;
} 