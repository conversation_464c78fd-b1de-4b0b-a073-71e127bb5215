package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 团队解散申请审核DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "团队解散申请审核DTO")
public class DistTeamDismissAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @NotNull(message = "申请ID不能为空")
    @Schema(description = "申请ID", required = true)
    private Long id;

    /**
     * 审核状态：approve-通过，reject-拒绝
     */
    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态：approve-通过，reject-拒绝", required = true)
    @Pattern(regexp = "^(approve|reject)$", message = "审核状态只能是approve或reject")
    private String status;

    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    private String remark;
} 