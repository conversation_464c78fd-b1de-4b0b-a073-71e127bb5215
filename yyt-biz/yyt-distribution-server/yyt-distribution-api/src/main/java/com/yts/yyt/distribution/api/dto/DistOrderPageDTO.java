package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 分销订单分页查询DTO
 *
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分销订单分页查询DTO")
public class DistOrderPageDTO extends BasePage {

    /**
     * 拍品名称或订单编号
     */
    @Schema(description = "拍品名称或订单编号")
    private String lotKeyword;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 分销订单状态：0已支付 1已发货 2已签收 3已完成 4已退款
     * @see com.yts.yyt.distribution.api.enums.DistOrderStatusEnum
     */
    @Schema(description = "分销订单状态：0已支付 1已发货 2已签收 3已完成 4已退款 为空表示查询全部")
    private Integer status;
}