package com.yts.yyt.distribution.api.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DistributionErrorEnum {

    OPERATION_FAIL(30000, "操作失败"),
    REMOTE_CALL_ERROR(30001, "远程调用失败"),
    MQ_SEND_ERROR(30002, "MQ发送失败"),
    USER_IS_TEAM_LEADER(30003, "当前已是团长"),
    PARAM_ERROR(30004, "参数错误"),
    TEAM_GMV_NEED_BIG_MAX_TEAM_GMV(30005, "团队Gmv需大于当前最大等级的团队Gmv"),
    GMV_NEED_BIG_MAX_GMV(30005, "个人Gmv需大于当前最大等级的个人Gmv"),
    //团员角色团长可得管理佣金不能为空
    LEADER_COMMISSION_RATE_NOT_NULL(30006, "团员角色团长可得管理佣金不能为空"),
    LEADER_MIN_DIRECT_NUM_NOT_NULL(30007, "团长角色直接下级人数下限不能为空"),
    //团长角色团队GMV下限不能为空
    LEADER_MIN_GMV_NOT_NULL(30008, "团长角色团队GMV下限不能为空"),

    ORDER_HAS_PAY(30009, "订单已支付,请勿重复支付"),

    PAY_CALLBACK_ERROR(30010, "支付回调失败"),

    DIST_TEAM_PAY_ORDER_ERROR(30011, "团长服务费下单失败"),

    TEAM_DISMISS_NOT_LEADER(30012, "您不是团长或团队不存在"),
    TEAM_DISMISS_STATUS_ERROR(30013, "团队状态异常，无法申请解散"),
    TEAM_DISMISS_DUPLICATE_APPLY(30014, "您已有待审核的解散申请，请勿重复申请"),
    TEAM_DISMISS_APPLY_FAILED(30015, "申请提交失败，请重试"),

    TEAM_NOT_LEADER(30016, "您不是团长或团队不存在"),
    TEAM_DISMISS_APPLY_NOT_FOUND(30017, "申请不存在"),
    TEAM_DISMISS_APPLY_ALREADY_PROCESSED(30018, "申请已处理，请勿重复处理"),
    TEAM_SERVICE_FEE_RETURN_NOT_FOUND(30019, "团长服务费退回异常，未找到原支付订单"),
    TEAM_SERVICE_FEE_RETURN_AMOUNT_ERROR(30020, "团长服务费退回异常，金额错误"),

    TEAM_SERVICE_FEE_NOT_FOUND(30021,"退款订单不存在"),
    TEAM_SERVICE_FEE_RETURN_PROCESSING(30022, "有正在处理的订单"),
    // 团队邀请相关错误码
    USER_NOT_TEAM_LEADER(30012, "用户不是正常状态的团长"),
    INVITE_CODE_INVALID(30013, "邀请码无效或已过期"),
    TEAM_NOT_EXIST(30014, "团队不存在或已解散"),
    ALREADY_TEAM_MEMBER(30015, "您已经是该团队成员"),
    INVITE_CODE_USED(30016, "邀请码已被使用"),

    USER_NOT_SUPPORT_APPLY_DISTRIBUTION(30017, "当前用户不支持申请分销"),
    UPDATE_DIST_STATUS_FAIL(30018, "更新拍品分销状态失败"),
    GOODS_LOT_NOT_FOUND(30019, "拍品信息不存在"),

    // 审核相关错误
    APPLY_NOT_FOUND(30020, "申请记录不存在"),
    APPLY_ALREADY_AUDITED(30021, "申请已被审核，不能重复审核"),
    INVALID_AUDIT_STATUS(30022, "无效的审核状态"),
    AUDIT_FAILED(30023, "审核操作失败"),
    APPLY_FAILED(30024, "申请提交失败"),
    DIST_ORDER_NOT_FOUND(30025, "分销订单不存在"),
    USER_LEVEL_NOT_FOUND(30026, "分销员等级信息不存在"),
    USER_ROLE_OR_LEVEL_NULL(30027, "分销员角色或等级为空"),
    LEVEL_RULE_NOT_FOUND(30028, "未找到对应的佣金规则"),
    TEAM_ID_NULL(30029, "团员订单未找到团队ID"),
    LEADER_LEVEL_NOT_FOUND(30030, "团长信息不存在"),
    LEADER_RULE_NOT_FOUND(30031, "未找到团长佣金规则"),
    UNKNOWN_ROLE_TYPE(30032, "未知角色类型"),
    //分销订单佣金计算异常
    DIST_ORDER_COMMISSION_CALC_ERROR(30033, "分销订单佣金计算异常"),

    QUERY_USER_LEVEL_FAIL(30025, "查询用户等级失败"),
    QUERY_LEVEL_RULE_FAIL(30026, "查询佣金配置失败"),
    SAVE_SHARE_RECORD_FAIL(30027, "保存分享记录失败"),
    GOODS_LOT_NOT_DISTRIBUTION(30028, "商品未开启分销或状态不可分销"),
    SHARE_RECORD_NOT_FOUND(30029, "分享记录不存在"),
    SAVE_CLICK_STATISTICS_FAIL(30030, "保存点击统计记录失败"),
    UPDATE_CLICK_STATISTICS_FAIL(30031, "更新点击统计记录失败"),
    GMV_NEED_LESS_THAN_NEXT_LEVEL_GMV(30032, "需小于下一个等级的GMV"),
    GMV_NEED_GREATER_THAN_PREV_LEVEL_GMV(30033, "需大于上一个等级的GMV"),
    ONLY_DELETE_TOP_LEVEL(30034, "只能删除最高等级规则"),


    // 操作账户资金失败
    OPERATION_ACCOUNT_FAIL(30034, "操作账户资金失败"),

    SHARE_CODE_NOT_EXIST(30035, "分享码不存在"),
    GET_ACCOUNT_ERROR(30036, "获取账户信息失败"),
    LEAST_ONE_LEVEL_RULE(30037, "至少保留一条规则"),
    TEAM_SERVICE_FEE_REFUND_FAIL(30038, "团长服务费退款失败"),



    ;


    private final Integer code;
    private final String msg;

    public void throwException() {
    	throw new DistributionException(this.msg,this.code);
    }

}
