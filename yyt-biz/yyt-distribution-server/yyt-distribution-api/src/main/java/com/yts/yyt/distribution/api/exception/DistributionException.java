package com.yts.yyt.distribution.api.exception;

import com.yts.yyt.common.core.exception.GlobalBizException;

public class DistributionException extends GlobalBizException {


    public DistributionException() {
        super();
    }

    public DistributionException(String msg) {
        super(msg);

    }

    public DistributionException(String msg, Throwable e) {
        super(msg, e);
    }

    public DistributionException(String msg, int code) {
        super(msg, code);

    }

    public DistributionException(DistributionErrorEnum codeEnum) {
        super(codeEnum.getMsg(), codeEnum.getCode());
    }

    public DistributionException(Integer code, String msg) {
        super(msg, code);

    }

    public DistributionException(String msg, int code, Throwable e) {
        super(msg, code, e);
    }

    public static DistributionException build(DistributionErrorEnum codeEnum) {
        return new DistributionException(codeEnum.getCode(), codeEnum.getMsg());
    }

}
