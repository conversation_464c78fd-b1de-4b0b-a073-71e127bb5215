package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 选品广场商品分页查询结果VO
 *
 * @since 2025-01-27
 */
@Data
@Schema(description = "选品广场商品分页查询结果VO")
public class GoodsSelectionPageVO {

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long goodsId;

    /**
     * 拍品ID
     */
    @Schema(description = "拍品ID")
    private Long lotId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodsName;

    /**
     * 商品主图
     */
    @Schema(description = "商品主图")
    private String mainImage;

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * 店铺头像
     */
    @Schema(description = "店铺头像")
    private String shopAvatar;

    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;

    /**
     * 年代
     */
    @Schema(description = "年代")
    private String dynasty;

    /**
     * 商品价格
     */
    @Schema(description = "商品价格")
    private BigDecimal price;

    /**
     * 分销佣金
     */
    @Schema(description = "分销佣金")
    private BigDecimal commission;

    /**
     * 佣金比例
     */
    @Schema(description = "佣金比例")
    private BigDecimal commissionRate;

    /**
     * 活动价格（折扣价格）
     */
    @Schema(description = "活动价格（折扣价格）")
    private BigDecimal activityPrice;
}