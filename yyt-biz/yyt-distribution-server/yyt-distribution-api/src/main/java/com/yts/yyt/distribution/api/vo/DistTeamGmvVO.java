package com.yts.yyt.distribution.api.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DistTeamGmvVO {
    @Schema(description = "拍品图片")
    private String lotImage;

    @Schema(description = "拍品名称")
    private String lotName;

    @Schema(description = "拍品售价")
    private BigDecimal lotPrice;

    @Schema(description = "下单时间")
    private LocalDateTime payTime;

    @Schema(description = "分享人")
    private String sharerName;

    @Schema(description = "分享人佣金")
    private BigDecimal sharerCommission;

    @Schema(description = "我的佣金")
    private BigDecimal myCommission;
} 
