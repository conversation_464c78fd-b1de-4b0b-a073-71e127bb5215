package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "团队服务费VO")
public class DistTeamServiceFeeVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "团长用户ID")
    private Long userId;

    @Schema(description = "支付金额（元）")
    private BigDecimal payAmount;

    @Schema(description = "支付渠道")
    private String payChannel;

    @Schema(description = "支付类型")
    private String orderType;

    @Schema(description = "支付状态")
    private String payStatus;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;
} 