package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 团队解散申请列表VO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "团队解散申请列表VO")
public class DistTeamDismissApplyListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @Schema(description = "申请ID")
    private Long id;

    /**
     * 团队名称
     */
    @Schema(description = "团队名称")
    private String teamName;

    /**
     * 团长手机号
     */
    @Schema(description = "团长手机号")
    private String mobile;

    /**
     * 团队GMV
     */
    @Schema(description = "团队GMV")
    private BigDecimal teamGmv;

    /**
     * 服务费
     */
    @Schema(description = "服务费")
    private BigDecimal serviceFee;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String status;

    /**
     * 等级名称
     */
    @Schema(description = "等级名称")
    private String levelName;

    @Schema(description = "团长名称")
    private String leaderName;
} 