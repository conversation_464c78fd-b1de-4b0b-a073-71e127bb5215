package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DistMyGmvPageDTO extends BasePage {
    @Schema(description = "月份，格式yyyy-MM")
    @NotNull(message = "月份不能为空")
    private String month;

    @Schema(description = "订单状态 1-已完成 0-未完成")
    @NotNull(message = "状态不能为空")
    private Integer orderStatus;
} 