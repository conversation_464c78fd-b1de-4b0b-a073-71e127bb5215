package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 拍品分销启/停审核记录VO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销启/停审核记录VO")
public class DistLotDistAuditVO {

    /**
     * ID（雪花ID）
     */
    @Schema(description = "ID（雪花ID）")
    private Long id;

    /**
     * 关联 dist_lot_dist_apply.id
     */
    @Schema(description = "关联 dist_lot_dist_apply.id")
    private Long applyId;

    /**
     * 审核人 ID（后台管理员）
     */
    @Schema(description = "审核人 ID（后台管理员）")
    private Long auditorId;

    /**
     * 审核人昵称快照
     */
    @Schema(description = "审核人昵称快照")
    private String auditorName;

    /**
     * 审核状态 通过=approve 拒绝=reject
     */
    @Schema(description = "审核状态 通过=approve 拒绝=reject")
    private String status;

    /**
     * 拒绝原因 / 备注
     */
    @Schema(description = "拒绝原因 / 备注")
    private String reason;

    /**
     * 审核动作时间
     */
    @Schema(description = "审核动作时间")
    private LocalDateTime auditTime;
}