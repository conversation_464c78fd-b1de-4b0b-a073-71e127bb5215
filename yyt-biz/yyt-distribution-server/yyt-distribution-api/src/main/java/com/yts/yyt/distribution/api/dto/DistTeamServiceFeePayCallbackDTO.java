package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class DistTeamServiceFeePayCallbackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @Schema(description="订单id")
    private String orderId;

    /**
     * 支付流水号
     */
    @Schema(description="支付流水号")
    private String paySerio;

    @Schema(description="支付状态")
    private Integer payStatus;

    @Schema(description="原交易请求流水号")
    private String orgReqSeqId;

    @Schema(description="原交易请求日期")
    private String orgReqDate;

    @Schema(description="交易订单号")
    private String outTransId;

    @Schema(description="微信支付宝的商户单号")
    private String partyOrderId;

    @Schema(description="支付类型")
    private String payType;

    @Schema(description="微信小程序openid")
    private String weAppOpenid;

    private LocalDateTime tradeTime;
}
