package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 拍品分销状态更新DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销状态更新DTO")
public class DistLotDistStatusUpdateDTO {

    /**
     * 拍品ID列表
     */
    @NotEmpty(message = "拍品ID列表不能为空")
    @Schema(description = "拍品ID列表")
    private List<Long> lotIds;

    /**
     * 分销状态：0-取消分销 1-开启分销
     */
    @NotNull(message = "分销状态不能为空")
    @Min(value = 0, message = "分销状态只能为0或1")
    @Max(value = 1, message = "分销状态只能为0或1")
    @Schema(description = "分销状态：0-取消分销 1-开启分销", example = "1")
    private Integer status;
}