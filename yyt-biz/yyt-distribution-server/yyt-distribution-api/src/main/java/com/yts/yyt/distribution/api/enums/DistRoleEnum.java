package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分销规则枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistRoleEnum {

    /**
     * 团长
     */
    LEADER("leader", "团长"),

    /**
     * 团员
     */
    MEMBER("member", "团员");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static DistRoleEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DistRoleEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}