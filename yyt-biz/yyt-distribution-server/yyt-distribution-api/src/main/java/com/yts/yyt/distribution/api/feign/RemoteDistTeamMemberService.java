package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.vo.DistUserTeamInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 分销团队成员 远程服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@FeignClient(contextId = "remoteDistTeamMemberService", value = ServiceNameConstants.DISTRIBUTION_SERVER, path = "/team/member")
public interface RemoteDistTeamMemberService {

    /**
     * 根据用户ID获取分销角色和团队信息
     *
     * @param userId 用户ID
     * @return 用户团队信息
     */
    @NoToken
    @PostMapping("/feign/getUserDistributionTeamInfo")
    R<DistUserTeamInfoVO> getUserDistributionTeamInfo(@RequestParam("userId") Long userId);

} 