package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 拍品分销管理批量操作DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销管理批量操作DTO")
public class DistLotInfoBatchDTO {

    /**
     * 拍品ID列表
     */
    @Schema(description = "拍品ID列表")
    @NotEmpty(message = "拍品ID列表不能为空")
    private List<Long> lotIds;

    /**
     * 操作类型：1-设为分销 0-取消分销
     */
    @Schema(description = "操作类型：1-设为分销 0-取消分销")
    @NotNull(message = "操作类型不能为空")
    private Integer operation;
} 