package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 拍品分销状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistLotStatusEnum {

    /**
     * 已分享：拍品开启分销且存在分享记录
     */
    SHARED("shared", "已分享"),

    /**
     * 未分享：拍品开启分销但不存在分享记录
     */
    NOT_SHARED("not_shared", "未分享"),

    /**
     * 未分销：dist_enable = 0
     */
    NOT_DISTRIBUTED("not_distributed", "未分销"),

    /**
     * 待审批：存在未审核的申请记录
     */
    PENDING("pending", "待审批");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 通过编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static DistLotStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (DistLotStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
} 