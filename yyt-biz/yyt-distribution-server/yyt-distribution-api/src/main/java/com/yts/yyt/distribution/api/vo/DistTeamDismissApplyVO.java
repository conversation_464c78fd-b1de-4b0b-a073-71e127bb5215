package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 团队解散申请VO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "团队解散申请VO")
public class DistTeamDismissApplyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID（雪花ID）
     */
    @Schema(description = "ID（雪花ID）")
    private Long id;

    /**
     * 团队 ID，对应 dist_team.id
     */
    @Schema(description = "团队 ID，对应 dist_team.id")
    private Long teamId;

    /**
     * 团长用户 ID
     */
    @Schema(description = "团长用户 ID")
    private Long leaderId;

    /**
     * 申请解散原因
     */
    @Schema(description = "申请解散原因")
    private String dismissReason;

    /**
     * 申请提交时间
     */
    @Schema(description = "申请提交时间")
    private LocalDateTime applyTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 