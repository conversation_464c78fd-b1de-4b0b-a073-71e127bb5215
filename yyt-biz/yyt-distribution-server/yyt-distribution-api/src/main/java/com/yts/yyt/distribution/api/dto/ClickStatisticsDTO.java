package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 点击统计请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Schema(description = "点击统计请求DTO")
public class ClickStatisticsDTO {

    /**
     * 短码
     */
    @Schema(description = "短码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "短码不能为空")
    private String shortCode;

} 