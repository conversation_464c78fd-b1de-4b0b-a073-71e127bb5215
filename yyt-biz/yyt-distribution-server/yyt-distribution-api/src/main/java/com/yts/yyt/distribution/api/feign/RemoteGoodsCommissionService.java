package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.dto.CalculateCommissionDTO;
import com.yts.yyt.distribution.api.vo.CalculateCommissionVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 远程佣金计算服务接口
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@FeignClient(contextId = "remoteGoodsCommissionService", value = ServiceNameConstants.DISTRIBUTION_SERVER,path = "/goods/selection")
public interface RemoteGoodsCommissionService {

    /**
     * 计算拍品佣金
     *
     * @param dto 计算佣金参数
     * @return 佣金信息
     */
    @NoToken
    @PostMapping("/feign/calculateCommission")
    R<CalculateCommissionVO> calculateCommission(@RequestBody CalculateCommissionDTO dto);

    /**
     * 记录拍品点击统计
     *
     * @param shortCode 分享短码
     */
    @NoToken
    @PostMapping("/feign/recordClickStatistics")
    void recordClickStatistics(@RequestParam("shortCode") String shortCode);

} 