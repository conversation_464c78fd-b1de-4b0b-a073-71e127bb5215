package com.yts.yyt.distribution.api.vo;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DistMyTeamVO {
    @Schema(description = "成员姓名")
    private String memberName;

    @Schema(description = "可提金额")
    private BigDecimal availableAmount;

    @Schema(description = "待结算金额")
    private BigDecimal freezeAmount;

    @Schema(description = "GMV")
    private BigDecimal gmv;

    @Schema(description = "状态（0-正常，1-已离团）")
    private Integer status;
} 