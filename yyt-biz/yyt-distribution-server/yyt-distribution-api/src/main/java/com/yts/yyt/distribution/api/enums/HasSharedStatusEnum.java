package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分享状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum HasSharedStatusEnum {

    /**
     * 未分享
     */
    NOT_SHARED(0, "未分享"),

    /**
     * 已分享
     */
    HAS_SHARED(1, "已分享");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举对象，未找到返回null
     */
    public static HasSharedStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (HasSharedStatusEnum value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断状态值是否有效
     *
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        return getByStatus(status) != null;
    }

    /**
     * 是否已分享
     *
     * @param status 状态值
     * @return 是否已分享
     */
    public static boolean isShared(Integer status) {
        return HAS_SHARED.getStatus().equals(status);
    }

    /**
     * 是否未分享
     *
     * @param status 状态值
     * @return 是否未分享
     */
    public static boolean isNotShared(Integer status) {
        return NOT_SHARED.getStatus().equals(status);
    }
} 