package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分销账户VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "分销账户VO")
public class DistAccountVO {

    /**
     * 用户ID（商家或平台）
     */
    @Schema(description = "用户ID（商家或平台）")
    private Long userId;

    /**
     * 总余额
     */
    @Schema(description = "总余额")
    private BigDecimal totalBalance;

    /**
     * 可提现余额
     */
    @Schema(description = "可提现余额")
    private BigDecimal availableBalance;

    /**
     * 冻结余额（待结算金额）
     */
    @Schema(description = "冻结余额（待结算金额）")
    private BigDecimal frozenBalance;

    /**
     * 提现中佣金
     */
    @Schema(description = "提现中佣金")
    private BigDecimal withdrawingBalance = BigDecimal.ZERO;

    /**
     * 已提现佣金
     */
    @Schema(description = "已提现佣金")
    private BigDecimal withdrawnBalance = BigDecimal.ZERO;

}