package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@Schema(description = "分销等级&佣金规则表-add")
public class DistLevelRuleAddDTO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 角色类型：团长=leader 团员=member
     */
    @Schema(description="团长=leader 团员=member")
    @NotNull(message = "角色类型不能为空")
    private String role;
    /**
     * 等级序号：1=初级 2=中级 3=高级 …
     */
    @Schema(description="等级序号：1=初级 2=中级 3=高级 …")
    private Integer levelCode;
    /**
     * 等级名称（初级 / 中级 / 高级）
     */
    @Schema(description="等级名称（初级 / 中级 / 高级）")
    @NotNull(message = "等级名称不能为空")
    private String levelName;
    /**
     * 团队GMV 下限（≥，单位元）
     */
    @Schema(description="团队GMV 下限（≥，单位元），ps：角色类型为团长时，不能为空")
    private BigDecimal minGmv;
    /**
     * 直接下级人数下限
     */
    @Schema(description="直接下级人数下限，ps：角色类型为团长时，不能为空")
    private Integer minMembers;
    /**
     * 个人 GMV 下限（元）
     */
    @Schema(description="个人GMV下限（元）")
    @NotNull(message = "个人GMV不能为空")
    private BigDecimal minPersonalGmv;
    /**
     * 自身佣金比例 %
     */
    @Schema(description="自身佣金比例 %")
    @NotNull(message = "自身佣金比例不能为空")
    private BigDecimal personalCommissionRate;
    /**
     * 团长可得管理佣金 %（仅团员等级时生效）
     */
    @Schema(description="团长可得管理佣金%，ps：角色类型为团员时，不能为空")
    private BigDecimal leaderCommissionRate;

}

