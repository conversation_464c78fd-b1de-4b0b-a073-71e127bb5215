package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.dto.DistLotInfoUpdateDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 分销拍品信息远程调用接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@FeignClient(contextId = "remoteDistLotInfoService", value = ServiceNameConstants.DISTRIBUTION_SERVER,path = "/lot/info")
public interface RemoteDistLotInfoService {

    /**
     * 更新拍品分销信息
     *
     * @param dto 更新参数
     * @return 更新结果
     */
    @NoToken
    @PostMapping("/update")
    R<Boolean> updateLotInfo(@Validated @RequestBody DistLotInfoUpdateDTO dto);

} 