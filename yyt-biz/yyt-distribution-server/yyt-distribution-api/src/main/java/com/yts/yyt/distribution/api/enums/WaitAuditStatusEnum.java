package com.yts.yyt.distribution.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 待审批状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@AllArgsConstructor
public enum WaitAuditStatusEnum {

    /**
     * 非待审批状态
     */
    NO_WAIT_AUDIT(0, "非待审批"),

    /**
     * 待审批状态
     */
    WAIT_AUDIT(1, "待审批");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举对象，未找到返回null
     */
    public static WaitAuditStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        for (WaitAuditStatusEnum value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断状态值是否有效
     *
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        return getByStatus(status) != null;
    }

    /**
     * 是否为待审批状态
     *
     * @param status 状态值
     * @return 是否为待审批
     */
    public static boolean isWaitAudit(Integer status) {
        return WAIT_AUDIT.getStatus().equals(status);
    }

    /**
     * 是否为非待审批状态
     *
     * @param status 状态值
     * @return 是否为非待审批
     */
    public static boolean isNotWaitAudit(Integer status) {
        return NO_WAIT_AUDIT.getStatus().equals(status);
    }
} 