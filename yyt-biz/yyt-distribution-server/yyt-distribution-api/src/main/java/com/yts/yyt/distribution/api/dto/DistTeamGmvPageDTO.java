package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DistTeamGmvPageDTO extends BasePage {
    @Schema(description = "月份，格式yyyy-MM")
    private String month;

    @Schema(description = "订单状态 1-已完成 0-未完成")
    private Integer orderStatus;

    @Schema(description = "历史团名id")
    private String teamId;
} 
