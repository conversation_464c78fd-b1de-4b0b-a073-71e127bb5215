package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DistMyCommissionVO {

    @Schema(description = "佣金金额")
    private BigDecimal commissionAmt;

    @Schema(description = "时间")
    private LocalDateTime createTime;
    /**
     * @see com.yts.yyt.distribution.api.enums.DistOrderCommissionStatusEnum
     */
    @Schema(description = "状态：normal-有效 invalid-失效")
    private String status;

}