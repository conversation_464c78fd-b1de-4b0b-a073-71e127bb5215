package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.distribution.api.enums.AuditStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 拍品分销审批DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销审批DTO")
public class DistLotInfoAuditDTO {

    /**
     * 拍品ID
     */
    @Schema(description = "拍品ID")
    @NotNull(message = "拍品ID不能为空")
    private Long lotId;

    /**
     * 审批结果：approve-通过 reject-拒绝
     */
    @Schema(description = "审批结果：approve-通过 reject-拒绝", allowableValues = {"approve", "reject"})
    @NotBlank(message = "审批结果不能为空")
    private String auditResult;
} 