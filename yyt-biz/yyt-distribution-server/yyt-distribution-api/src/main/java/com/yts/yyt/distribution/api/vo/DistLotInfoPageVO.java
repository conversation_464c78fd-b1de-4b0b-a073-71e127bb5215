package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 拍品分销管理分页查询结果VO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销管理分页查询结果VO")
public class DistLotInfoPageVO {

    /**
     * 主键ID（雪花ID）
     */
    @Schema(description = "主键ID（雪花ID）")
    private Long id;

    /**
     * 拍品 ID（goods_lot_info.id）
     */
    @Schema(description = "拍品 ID（goods_lot_info.id）")
    private Long lotId;

    /**
     * 拍品缩略图 URL
     */
    @Schema(description = "拍品缩略图 URL")
    private String lotImg;

    /**
     * 拍品名称
     */
    @Schema(description = "拍品名称")
    private String lotName;

    /**
     * 拍品编码 / 编号
     */
    @Schema(description = "拍品编码 / 编号")
    private String lotCode;

    /**
     * 商家ID
     */
    @Schema(description = "商家ID")
    private Long merchantId;

    /**
     * 商家名称
     */
    @Schema(description = "商家名称")
    private String merchantName;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal salePrice;

    /**
     * 链接查看数
     */
    @Schema(description = "链接查看数")
    private Integer linkViewCnt;

    /**
     * 分销状态：1-分销中 0-非分销 {@link com.yts.yyt.distribution.api.enums.DistEnableStatusEnum}
     */
    @Schema(description = "分销状态：1-分销中 0-非分销")
    private Integer distEnable;

    /**
     * 分享状态：1-已分享 0-未分享 {@link com.yts.yyt.distribution.api.enums.HasSharedStatusEnum}
     */
    @Schema(description = "分享状态：1-已分享 0-未分享")
    private Integer hasShared;

    /**
     * 待审批状态：1-待审批 0-非待审批 {@link com.yts.yyt.distribution.api.enums.WaitAuditStatusEnum}
     */
    @Schema(description = "待审批状态：1-待审批 0-非待审批")
    private Integer waitAudit;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 