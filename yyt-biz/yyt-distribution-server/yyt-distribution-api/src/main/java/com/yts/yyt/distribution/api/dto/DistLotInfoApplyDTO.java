package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 拍品分销申请DTO
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@Schema(description = "拍品分销申请DTO")
public class DistLotInfoApplyDTO {

    /**
     * 拍品 lot 主键列表（goods_lot_info.id）
     */
    @NotEmpty(message = "拍品ID列表不能为空")
    @Schema(description = "拍品 lot 主键列表")
    private List<Long> lotIds;

    /**
     * 申请动作：取消分销=cancel 恢复分销=recovery
     */
    @NotBlank(message = "申请动作不能为空")
    @Schema(description = "申请动作：取消分销=cancel 恢复分销=recovery")
    private String action;
} 