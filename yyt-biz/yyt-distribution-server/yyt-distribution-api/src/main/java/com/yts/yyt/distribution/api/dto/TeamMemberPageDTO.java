package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TeamMemberPageDTO extends BasePage {
    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "成员名称")
    private String memberName;

    @Schema(description = "手机号")
    private String mobile;
} 