package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "分销等级&佣金规则表-VO")
public class DistLevelRuleVO {

    /**
     * 主键 ID（雪花ID）
     */
    @Schema(description = "主键 ID（雪花ID）")
    private Long id;

    /**
     * 团长=leader 团员=member
     */
    @Schema(description = "团长=leader 团员=member")
    private String role;

    /**
     * 等级序号：1=初级 2=中级 3=高级 …
     */
    @Schema(description = "等级序号：1=初级 2=中级 3=高级 …")
    private Integer levelCode;

    /**
     * 等级名称（初级 / 中级 / 高级）
     */
    @Schema(description = "等级名称（初级 / 中级 / 高级）")
    private String levelName;

    /**
     * GMV 下限（≥，单位元）
     */
    @Schema(description = "GMV 下限（≥，单位元）")
    private BigDecimal minGmv;

    /**
     * 直接下级人数下限
     */
    @Schema(description = "直接下级人数下限")
    private Integer minMembers;

    /**
     * 个人 GMV 下限（元）
     */
    @Schema(description = "个人 GMV 下限（元）")
    private BigDecimal minPersonalGmv;

    /**
     * 自身佣金比例 %
     */
    @Schema(description = "自身佣金比例 %")
    private BigDecimal personalCommissionRate;

    /**
     * 团长可得管理佣金 %（仅团员等级时生效）
     */
    @Schema(description = "团长可得管理佣金 %（仅团员等级时生效）")
    private BigDecimal leaderCommissionRate;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
} 