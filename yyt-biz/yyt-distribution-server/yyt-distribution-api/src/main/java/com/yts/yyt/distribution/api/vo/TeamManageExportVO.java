package com.yts.yyt.distribution.api.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "团队管理导出VO")
public class TeamManageExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("团队ID")
    private Long teamId;

    @ExcelProperty("团队名称")
    private String teamName;

    @ExcelProperty("团长")
    private String leaderName;

    @ExcelProperty("团长电话")
    private String leaderPhone;

    @ExcelProperty("团长等级")
    private String leaderLevelName;

    @ExcelProperty("团员数量")
    private Integer memberCount;

    @ExcelProperty("团队累计GMV")
    private BigDecimal teamGmv;

    @ExcelProperty("团队累计已售商品数")
    private Integer productSoldCount;

    @ExcelProperty("团队累计佣金")
    private BigDecimal commissionTotal;

    @ExcelProperty("组团时间")
    private LocalDateTime createTime;

    @ExcelProperty("团队服务费")
    private BigDecimal serviceFee;

    @ExcelProperty("状态")
    private String status;
} 
