package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 选品广场商品详情VO
 *
 * @since 2025-01-27
 */
@Data
@Schema(description = "选品广场商品详情VO")
public class GoodsSelectionDetailVO {

    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long goodsId;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String goodsName;

    /**
     * 商品图片列表
     */
    @Schema(description = "商品图片列表")
    private List<String> images;

    /**
     * 商品价格
     */
    @Schema(description = "商品价格")
    private BigDecimal price;

    /**
     * 分销佣金
     */
    @Schema(description = "分销佣金")
    private BigDecimal commission;

    /**
     * 佣金比例
     */
    @Schema(description = "佣金比例")
    private BigDecimal commissionRate;

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;

    /**
     * 年代
     */
    @Schema(description = "年代")
    private String dynasty;

    /**
     * 品相
     */
    @Schema(description = "品相")
    private String condition;

    /**
     * 窑口
     */
    @Schema(description = "窑口")
    private String kiln;

    /**
     * 尺寸
     */
    @Schema(description = "尺寸")
    private String size;

    /**
     * 藏品描述
     */
    @Schema(description = "藏品描述")
    private String description;

    /**
     * 是否已鉴定：0=否 1=是
     */
    @Schema(description = "是否已鉴定：0=否 1=是")
    private Integer isAuthenticated;

    /**
     * 是否30天无理由退货：0=否 1=是
     */
    @Schema(description = "是否30天无理由退货：0=否 1=是")
    private Integer isReturnAllowed;

    /**
     * 是否已溯源：0=否 1=是
     */
    @Schema(description = "是否已溯源：0=否 1=是")
    private Integer isTraced;

    /**
     * 鉴定证书图片
     */
    @Schema(description = "鉴定证书图片")
    private String certificateImage;

    /**
     * 商品状态：available=可分销 unavailable=不可分销
     */
    @Schema(description = "商品状态：available=可分销 unavailable=不可分销")
    private String status;
} 