package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金明细分页查询结果VO
 *
 * @since 2025-01-27
 */
@Data
@Schema(description = "佣金明细分页查询结果VO")
public class CommissionDetailPageVO {

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 待结算佣金
     */
    @Schema(description = "待结算佣金")
    private BigDecimal pendingCommission;

    /**
     * 可提现佣金
     */
    @Schema(description = "可提现佣金")
    private BigDecimal withdrawableCommission;

    /**
     * 结算时间
     */
    @Schema(description = "结算时间")
    private LocalDateTime settlementTime;

    /**
     * 佣金状态
     */
    @Schema(description = "状态 normal:正常 invalid:无效")
    private String status;
} 