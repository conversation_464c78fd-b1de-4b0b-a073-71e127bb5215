package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import com.yts.yyt.common.core.util.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 团队解散申请查询DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "团队解散申请查询DTO")
public class DistTeamDismissApplyQueryDTO extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "查询参数")
    private String queryKeyWords;

    /**
     * 审核状态
     * @see com.yts.yyt.distribution.api.constants.TeamDismissStatusEnum
     */
    @Schema(description = "审核状态")
    private String status;

    /**
     * 申请开始时间
     */
    @Schema(description = "申请开始时间")
    private LocalDateTime startTime;

    /**
     * 申请结束时间
     */
    @Schema(description = "申请结束时间")
    private LocalDateTime endTime;
} 