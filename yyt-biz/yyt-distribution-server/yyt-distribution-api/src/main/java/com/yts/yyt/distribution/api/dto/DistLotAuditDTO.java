package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 拍品分销启/停审核DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销启/停审核DTO")
public class DistLotAuditDTO {

    /**
     * 申请ID
     */
    @NotNull(message = "申请ID不能为空")
    @Schema(description = "申请ID")
    private Long applyId;

    /**
     * 审核状态 通过=approve 拒绝=reject
     */
    @NotBlank(message = "审核状态不能为空")
    @Schema(description = "审核状态 通过=approve 拒绝=reject")
    private String status;
}