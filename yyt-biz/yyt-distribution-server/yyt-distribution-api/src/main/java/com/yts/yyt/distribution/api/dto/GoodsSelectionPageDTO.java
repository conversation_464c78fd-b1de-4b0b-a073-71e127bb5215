package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 选品广场商品分页查询DTO
 *
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "选品广场商品分页查询DTO")
public class GoodsSelectionPageDTO extends BasePage {

    /**
     * 店铺/商品名称关键字
     */
    @Schema(description = "店铺/商品名称关键字")
    private String keyword;

    /**
     * 年代
     */
    @Schema(description = "年代")
    private List<String> dynasty;

    /**
     * 价格范围-最低价
     */
    @Schema(description = "价格范围-最低价")
    private BigDecimal minPrice;

    /**
     * 价格范围-最高价
     */
    @Schema(description = "价格范围-最高价")
    private BigDecimal maxPrice;
} 