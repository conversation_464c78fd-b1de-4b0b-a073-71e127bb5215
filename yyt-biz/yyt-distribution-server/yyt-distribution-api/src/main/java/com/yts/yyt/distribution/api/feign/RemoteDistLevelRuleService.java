package com.yts.yyt.distribution.api.feign;

import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.feign.annotation.NoToken;
import com.yts.yyt.distribution.api.dto.DistLevelRuleQueryDTO;
import com.yts.yyt.distribution.api.vo.DistLevelRuleVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 分销等级&佣金规则表 远程服务接口
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@FeignClient(contextId = "remoteDistLevelRuleService", value = ServiceNameConstants.DISTRIBUTION_SERVER,path = "/distLevelRule")
public interface RemoteDistLevelRuleService {

    /**
     * 根据角色获取分销等级&佣金规则列表
     *
     * @param role 角色
     * @return 分销等级&佣金规则列表
     */
    @NoToken
    @PostMapping("/getListByRole")
    R<List<DistLevelRuleVO>> getListByRole(@RequestParam(required = false) String role);

    /**
     * 根据ID获取分销等级&佣金规则
     *
     * @param id 分销等级&佣金规则ID
     * @return 分销等级&佣金规则
     */
    @NoToken
    @PostMapping("/queryById")
    R<DistLevelRuleVO> queryById(@RequestParam Long id);

    /**
     * 根据levelCode和role查询分销等级&佣金规则
     *
     * @param dto 等级编码
     * @return 分销等级&佣金规则
     */
    @NoToken
    @PostMapping("/queryByLevelCodeAndRole")
    R<DistLevelRuleVO> queryByLevelCodeAndRole(@RequestBody DistLevelRuleQueryDTO dto);

}
