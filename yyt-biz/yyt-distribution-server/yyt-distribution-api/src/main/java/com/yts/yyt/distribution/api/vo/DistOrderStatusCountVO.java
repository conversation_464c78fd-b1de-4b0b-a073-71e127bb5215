package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分销订单状态统计VO
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "分销订单状态统计VO")
public class DistOrderStatusCountVO {

    /**
     * 总数量
     */
    @Schema(description = "总数量")
    private Long totalCount;

    /**
     * 已支付数量（状态：0）
     */
    @Schema(description = "已支付数量（状态：0）")
    private Long paidCount;

    /**
     * 已发货数量（状态：1）
     */
    @Schema(description = "已发货数量（状态：1）")
    private Long shippedCount;

    /**
     * 已签收数量（状态：2）
     */
    @Schema(description = "已签收数量（状态：2）")
    private Long receivedCount;

    /**
     * 已完成数量（状态：3）
     */
    @Schema(description = "已完成数量（状态：3）")
    private Long completedCount;

    /**
     * 已退款数量（状态：4）
     */
    @Schema(description = "已退款数量（状态：4）")
    private Long refundedCount;
} 