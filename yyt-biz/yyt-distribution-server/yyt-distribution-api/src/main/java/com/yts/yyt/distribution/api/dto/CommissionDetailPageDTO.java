package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * 佣金明细分页查询DTO
 *
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "佣金明细分页查询DTO")
public class CommissionDetailPageDTO extends BasePage {

    /**
     * 分销员ID
     */
    @NotNull(message = "分销员ID不能为空")
    @Schema(description = "分销员ID")
    private Long distributorId;
} 