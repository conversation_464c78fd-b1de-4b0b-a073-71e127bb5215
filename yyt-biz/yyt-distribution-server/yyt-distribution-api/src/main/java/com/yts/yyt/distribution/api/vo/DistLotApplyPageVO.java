package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 拍品分销启/停申请分页查询结果VO
 *
 * @since 2025-06-17
 */
@Data
@Schema(description = "拍品分销启/停申请分页查询结果VO")
public class DistLotApplyPageVO {

    /**
     * ID（雪花ID）
     */
    @Schema(description = "ID（雪花ID）")
    private Long id;

    /**
     * 拍品 lot 主键（goods_lot_info.id）
     */
    @Schema(description = "拍品 lot 主键（goods_lot_info.id）")
    private Long lotId;

    /**
     * 拍品名称
     */
    @Schema(description = "拍品名称")
    private String lotName;
    /**
     * 拍品照片
     */
    @Schema(description = "拍品照片")
    private String mainImage;
    /**
     * 拍品编码
     */
    @Schema(description = "拍品编码")
    private String lotCode;
    /**
     * 商家名称
     */
    @Schema(description = "商家名称")
    private String merchantName;
    /**
     * 商家id
     */
    @Schema(description = "商家id")
    private Long merchantId;
    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private String salePrice;
    /**
     * 链接查看数
     */
    @Schema(description = "链接查看数")
    private Long viewCount;
}