package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class DistUserLevelVO {

    /**
     * 当前等级数字（1 / 2 / 3 …）
     */
    @Schema(description="当前等级数字（1 / 2 / 3 …）")
    private Integer levelCode;
    /**
     * 等级中文名快照（例：初级 / 中级 / 高级）
     */
    @Schema(description="等级中文名快照（例：初级 / 中级 / 高级）")
    private String levelName;
    /**
     * 个人佣金比例
     */
    @Schema(description="个人佣金比例")
    private BigDecimal personalCommissionRate;

}

