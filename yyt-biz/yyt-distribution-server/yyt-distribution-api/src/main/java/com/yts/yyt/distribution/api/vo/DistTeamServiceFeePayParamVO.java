package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "团长服务费支付参数")
@Accessors(chain = true)
public class DistTeamServiceFeePayParamVO implements Serializable {
    public final long serialVersionUID = 1L;


    public static final String PAYBACK = "DIST_TEAM_SERVICE_FEE_PAYBACK";

    @Schema(description = "订单ID")
    private Long orderId;


    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "交易金额")
    private BigDecimal amount;

    @Schema(description = "交易手续费")
    private BigDecimal fee;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品编号")
    private String goodsNo;

    @Schema(description = "支付订单类型,支付时用到")
    private String orderType;

    /**
     * 子商户用户标识
     * 公众号和小程序场景必填。用户在子商户sub_appid下的唯一标识
     */
    @Schema(description="用户在子商户sub_appid下的唯一标识")
    private String wxSubOpenid;

    /**
     * 过期时间 YYYY-MM-DD HH:ss:MM
     */
    @Schema(description="过期时间")
    private LocalDateTime expirseTime;

    /**
     * 手续费扣款标志 1: 外扣 2: 内扣
     */
    @Schema(description="手续费扣款标志 1: 外扣 2: 内扣")
    private String feeFlag;

    @Schema(description="延时结算标志 Y 为延迟 N 为不延迟")
    private String delayAcctFlag;

    @Schema(description="分账标志 Y 为分账 N 为不分账")
    private String acctSplitBunchFlag;

    @Schema(description = "指定入帐户")
    private String acctId;



}
