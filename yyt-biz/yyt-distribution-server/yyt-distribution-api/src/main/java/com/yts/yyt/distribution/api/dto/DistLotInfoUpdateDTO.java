package com.yts.yyt.distribution.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 拍品分销信息更新DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Schema(description = "拍品分销信息更新DTO")
public class DistLotInfoUpdateDTO {

    /**
     * 拍品ID列表
     */
    @Schema(description = "拍品ID列表")
    private List<Long> lotIds;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private BigDecimal salePrice;

    /**
     * 拍品名称
     */
    @Schema(description = "拍品名称")
    private String lotName;
} 