package com.yts.yyt.distribution.api.dto;

import com.yts.yyt.common.core.entity.BasePage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 业绩结算分页查询DTO
 *
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "业绩结算分页查询DTO")
public class CommissionSettlementPageDTO extends BasePage {

    /**
     * 分销员名称或编号
     */
    @Schema(description = "分销员名称或编号")
    private String distributorKeyword;

    /**
     * 结算开始时间
     */
    @Schema(description = "结算开始时间")
    private LocalDateTime settlementStartTime;

    /**
     * 结算结束时间
     */
    @Schema(description = "结算结束时间")
    private LocalDateTime settlementEndTime;
} 