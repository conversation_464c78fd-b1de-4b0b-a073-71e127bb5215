package com.yts.yyt.distribution.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 计算佣金结果VO
 */
@Data
@Schema(description = "计算佣金结果")
public class CalculateCommissionVO {

	/**
	 * 佣金金额
	 */
	@Schema(description = "佣金金额")
	private BigDecimal commissionAmount;

	/**
	 * 是否是分销员
	 */
	@Schema(description = "是否是分销员 1是 0否")
	private Integer isDistributor;

} 