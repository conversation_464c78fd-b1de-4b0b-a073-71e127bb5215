<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yts</groupId>
        <artifactId>yyt-distribution-server</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>yyt-distribution-api</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--core 工具类-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-excel</artifactId>
        </dependency>
        <!-- 脱敏工具类-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-sensitive</artifactId>
        </dependency>
    </dependencies>

</project>