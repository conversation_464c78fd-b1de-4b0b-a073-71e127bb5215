//package com.yts.yyt.merchant.service;
//
//import com.alibaba.fastjson.JSON;
//import com.yts.yyt.common.pay.huifu.dto.*;
//import com.yts.yyt.common.pay.huifu.service.HuifuUserBusiService;
//import com.yts.yyt.common.pay.huifu.vo.*;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.Collections;
//
//@Slf4j
//public class UserEntryServiceTest extends BaseSpringTest{
//
//	@Autowired
//	private HuifuUserBusiService huifuUserBusiService;
//
////	private static final String HUIFU_ID = "****************"; // 陈航
//	private static final String HUIFU_ID = "****************"; // 甘怡雯  4.4
//	/**
//	 * [{
//	 * 	"cashType": "T1",
//	 * 	"outCashHuifuId": "",
//	 * 	"feeRate": "0.00",
//	 * 	"outCashFlag": "2",
//	 * 	"fixAmt": "1.00",
//	 * 	"switchState": "1"
//	 * }]
//	 * [{
//	 * 	"bankCode": "********",
//	 * 	"certType": "00",
//	 * 	"mp": "***********",
//	 * 	"cardName": "甘怡雯",
//	 * 	"cardType": "1",
//	 * 	"branchName": "",
//	 * 	"bankName": "招商银行",
//	 * 	"certBeginDate": "********",
//	 * 	"tokenNo": "***********",
//	 * 	"cardNo": "****************",
//	 * 	"branchCode": "",
//	 * 	"certNo": "52011120000807004X",
//	 * 	"areaId": "520100",
//	 * 	"certEndDate": "********",
//	 * 	"certValidityType": "1",
//	 * 	"isSettleDefault": "",
//	 * 	"provId": "520000",
//	 * 	"status": "N"
//	 * }, {
//	 * 	"bankCode": "********",
//	 * 	"certType": "00",
//	 * 	"mp": "***********",
//	 * 	"cardName": "甘怡雯",
//	 * 	"cardType": "1",
//	 * 	"branchName": "",
//	 * 	"bankName": "交通银行",
//	 * 	"certBeginDate": "********",
//	 * 	"tokenNo": "***********",
//	 * 	"cardNo": "6222620580010864762",
//	 * 	"branchCode": "",
//	 * 	"certNo": "52011120000807004X",
//	 * 	"areaId": "520100",
//	 * 	"certEndDate": "********",
//	 * 	"certValidityType": "1",
//	 * 	"isSettleDefault": "",
//	 * 	"provId": "520000",
//	 * 	"status": "N"
//	 * }]
//	 */
////	private static final String HUIFU_ID = "****************"; // 菊姨  1.5
//	/**
//	 * [{
//	 * 	"cashType": "T1",
//	 * 	"outCashHuifuId": "",
//	 * 	"feeRate": "0.00",
//	 * 	"outCashFlag": "2",
//	 * 	"fixAmt": "0.00",
//	 * 	"switchState": "1"
//	 * }]
//	 * [{
//	 * 	"bankCode": "********",
//	 * 	"certType": "00",
//	 * 	"mp": "***********",
//	 * 	"cardName": "王兴菊",
//	 * 	"cardType": "1",
//	 * 	"branchName": "",
//	 * 	"bankName": "贵州省农村信用社联合社",
//	 * 	"certBeginDate": "********",
//	 * 	"tokenNo": "***********",
//	 * 	"cardNo": "6217790001160258717",
//	 * 	"branchCode": "",
//	 * 	"certNo": "522724199409174422",
//	 * 	"areaId": "140200",
//	 * 	"certEndDate": "********",
//	 * 	"certValidityType": "1",
//	 * 	"isSettleDefault": "",
//	 * 	"provId": "140000",
//	 * 	"status": "N"
//	 * }, {
//	 * 	"bankCode": "********",
//	 * 	"certType": "00",
//	 * 	"mp": "***********",
//	 * 	"cardName": "王兴菊",
//	 * 	"cardType": "1",
//	 * 	"branchName": "",
//	 * 	"bankName": "中国银行",
//	 * 	"certBeginDate": "********",
//	 * 	"tokenNo": "***********",
//	 * 	"cardNo": "6217562800012364524",
//	 * 	"branchCode": "",
//	 * 	"certNo": "522724199409174422",
//	 * 	"areaId": "520100",
//	 * 	"certEndDate": "********",
//	 * 	"certValidityType": "1",
//	 * 	"isSettleDefault": "",
//	 * 	"provId": "520000",
//	 * 	"status": "N"
//	 * }]
//	 */
//	@Test
//	public void userBusiOpenTest() {
//
//		UserBusiOpenDTO dto = new UserBusiOpenDTO();
//		dto.setHuifuId(HUIFU_ID);
//
//		UserBusiOpenCardInfoDTO cardInfoDTO = new UserBusiOpenCardInfoDTO();
//		cardInfoDTO.setCardType("1");
//		cardInfoDTO.setCardName("甘怡雯");
//		cardInfoDTO.setCardNo("6222620580010864762");
//		cardInfoDTO.setAreaId("520100");
//		cardInfoDTO.setProvId("520000");
//		cardInfoDTO.setIsSettleDefault("Y");·
//
//		CashConfig cashConfig = new CashConfig();
//		cashConfig.setCashType("T1");
//		cashConfig.setFixAmt("1.00");
//		cashConfig.setOutFeeFlag("2");
//
////		dto.setCardInfo(cardInfoDTO);
//		dto.setCashConfig(Collections.singletonList(cashConfig));
//		UserBusiOpenResultVO userBusiOpenResultVO = huifuUserBusiService.userBusiOpen(dto);
//		log.info("业务入驻结果:{}", JSON.toJSON(userBusiOpenResultVO));
//	}
//
//	@Test
//	public void userBusiModifyTest() {
//
//		UserBusiModifyDTO dto = new UserBusiModifyDTO();
//		dto.setHuifuId(HUIFU_ID);
//
//		UserBusiOpenCardInfoDTO cardInfoDTO = new UserBusiOpenCardInfoDTO();
//		cardInfoDTO.setCardType("1");
//		cardInfoDTO.setCardName("甘怡雯");
//		cardInfoDTO.setCardNo("6222620580010864762");
//		cardInfoDTO.setAreaId("520100");
//		cardInfoDTO.setProvId("520000");
//		cardInfoDTO.setIsSettleDefault("Y");
//
//		CashConfig cashConfig = new CashConfig();
//		cashConfig.setSwitchState("1");
//		cashConfig.setCashType("T1");
//		cashConfig.setFixAmt("1.00");
//		cashConfig.setOutFeeFlag("2");
//
//		dto.setCardInfo(cardInfoDTO);
//		dto.setCashConfig(Collections.singletonList(cashConfig));
//		UserBusiModifyResultVO userBusiModifyResultVO = huifuUserBusiService.userBusiModify(dto);
//		log.info("业务入驻修改结果:{}", JSON.toJSON(userBusiModifyResultVO));
//	}
//
//	@Test
//	public void queryUserInfoTest() {
//		UserInfoQueryDTO dto = new UserInfoQueryDTO();
//		dto.setHuifuId(HUIFU_ID);
//		UserInfoQueryResultVO result = huifuUserBusiService.queryUserInfo(dto);
//		// 把result中的数据分别打印出来
//		log.info("企业用户信息:{}", JSON.toJSON(result.getEntBaseInfo()));
//		log.info("个人用户信息:{}", JSON.toJSON(result.getIndvBaseInfo()));
//		log.info("提现配置信息:{}", JSON.toJSON(result.getQryCashConfigList()));
//		log.info("结算配置信息:{}", JSON.toJSON(result.getSettleConfig()));
//		log.info("银行卡配置信息:{}", JSON.toJSON(result.getCardInfo()));
//		log.info("取现卡信息:{}", JSON.toJSON(result.getQryCashCardInfoList()));
//
//	}
//
//	@Test
//	public void queryUserInfoListTest() {
//		// ****************
//		// 6666000167013723
//		UserInfoQueryListDTO dto = new UserInfoQueryListDTO();
//		dto.setLegalCertNo("522132199406082837");
//		UserInfoQueryListResultVO list = huifuUserBusiService.queryUserInfoList(dto);
//		log.info(JSON.toJSONString(list));
//	}
//
//	@Test
//	public void queryUserBalanceTest() {
//		UserBalanceQueryDTO dto = new UserBalanceQueryDTO();
//		dto.setHuifuId(HUIFU_ID);
//		UserBalanceQueryResultVO result = huifuUserBusiService.queryUserBalance(dto);
//		log.info(JSON.toJSONString(result));
//	}
//}
