package com.yts.yyt.merchant.service;

import com.alibaba.fastjson.JSON;
import com.yts.yyt.merchant.api.dto.MerchantAddDTO;
import com.yts.yyt.merchant.api.entity.CityPartnerEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class CityPartServiceTest extends BaseSpringTest{

    @Autowired
    CityPartnerService cityPartnerService;

    @Autowired
    MerchantService merchantService;

    @Test
    public void test_add(){

        CityPartnerEntity cityPartnerEntity = new CityPartnerEntity();

        cityPartnerEntity.setPartnerName("大帅三");
        cityPartnerEntity.setContactPhone("13179640727");
        cityPartnerEntity.setContactPerson("测试三");
        cityPartnerEntity.setIdCard("340222198812103413");

        cityPartnerEntity.setRegion("石家庄市");
        cityPartnerEntity.setStatus("enable");
        cityPartnerService.add(cityPartnerEntity);

    }

    @Test
    public void test_addMer(){
        String s = "{\"shopAvatar\":\"https://yyttest-**********.cos.ap-guangzhou.myqcloud.com/yyt/goods/d55f90518f0f45618266832c8bef13b3.png\",\"shopName\":\"空空如也\",\"shopType\":\"1\",\"contactPersonName\":\"空空\",\"contactPhone\":\"***********\",\"province\":\"北京市\",\"city\":\"北京市\",\"returnAddress\":\"是工工\",\"merchantEnterpriseName\":\"\",\"unifiedSocialCreditCode\":\"\",\"shopIntroduction\":\"\",\"businessLicense\":\"https://yyttest-**********.cos.ap-guangzhou.myqcloud.com/yyt/goods/35a1d23df7a84ce2b40f42f0682cb060.png\",\"idFrontUrl\":\"\",\"idBackUrl\":\"\",\"id\":\"\",\"state\":1,\"username\":\"\"}";
        MerchantAddDTO dto = JSON.parseObject(s, MerchantAddDTO.class);
        merchantService.add(dto);

    }

}
