server:
  port: 4000

spring:
  profiles:
    active: @profiles.active@
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: @nacos.ip@
        namespace: @profiles.active@
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: @profiles.active@
  config:
    import:
      - optional:nacos:application.yml
      - optional:nacos:${spring.application.name}.yml
      - optional:nacos:yyt-common-config.yml
      - optional:nacos:seata-config.yml



