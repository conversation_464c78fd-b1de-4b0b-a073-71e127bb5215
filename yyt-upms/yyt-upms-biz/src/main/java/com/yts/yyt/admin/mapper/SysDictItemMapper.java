/*
 *    Copyright (c) 2018-2025, yyt All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: yyt
 */
package com.yts.yyt.admin.mapper;

import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.common.data.datascope.YytBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 字典项
 *
 * <AUTHOR>
 * @date 2019/03/19
 */
@Mapper
public interface SysDictItemMapper extends YytBaseMapper<SysDictItem> {

}
