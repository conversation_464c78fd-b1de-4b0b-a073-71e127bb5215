/*
 *    Copyright (c) 2018-2025, yyt All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: yyt
 */
package com.yts.yyt.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.admin.api.dto.SysFileGroupDTO;
import com.yts.yyt.admin.api.dto.SysFileLogDTO;
import com.yts.yyt.admin.api.entity.SysFile;
import com.yts.yyt.admin.api.entity.SysFileGroup;
import com.yts.yyt.admin.api.vo.SysFileVO;
import com.yts.yyt.admin.api.vo.UploadFileVO;
import com.yts.yyt.admin.mapper.SysFileGroupMapper;
import com.yts.yyt.admin.mapper.SysFileMapper;
import com.yts.yyt.admin.service.SysFileService;
import com.yts.yyt.common.core.constant.CacheConstants;
import com.yts.yyt.common.core.constant.CommonConstants;
import com.yts.yyt.common.core.constant.enums.FileTypeEnum;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.data.tenant.TenantContextHolder;
import com.yts.yyt.common.file.core.FileProperties;
import com.yts.yyt.common.file.core.FileTemplate;
import com.yts.yyt.user.api.constant.enums.FileCompressEnum;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.amazonaws.Protocol.HTTPS;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2019-06-18 17:18:42
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements SysFileService {

    private final FileTemplate fileTemplate;

    private final SysFileGroupMapper fileGroupMapper;

    private final FileProperties properties;

    private CacheManager cacheManager;

    /**
     * 上传文件
     *
     * @param file    文件流
     * @param dir     文件夹
     * @param groupId 分组ID
     * @param type    类型
     * @return
     */
    @Override
    public R uploadFile(MultipartFile file, String dir, Long groupId, String type) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());

        try (InputStream inputStream = file.getInputStream()) {
            fileTemplate.putObject(properties.getBucketName(), dir, fileName, inputStream, file.getContentType());
            // 文件管理数据记录,收集管理追踪文件
            fileLog(file, dir, fileName, groupId, type);
        } catch (Exception e) {
            log.error("上传失败", e);
            return R.failed(e.getLocalizedMessage());
        }

        // 显示文件地址
//        String fileShowUrl = String.format("/admin/sys-file/oss/file?fileName=%s", fileName);
		String fileShowUrl = toOssShowUrl(dir, fileName);
        // 返回数据
        return R.ok(new UploadFileVO(properties.getBucketName(), fileName, fileShowUrl));
    }

	private String toOssShowUrl(String dir, String fileName) {
		String endpoint = properties.getOss().getEndpoint();
		endpoint = Validator.isUrl(endpoint) ? URLUtil.url(endpoint).getHost() : endpoint;
		String baseUrl = HTTPS + StrUtil.COLON + StrUtil.SLASH + StrUtil.SLASH + properties.getBucketName() + StrUtil.DOT + endpoint;
		return StrUtil.isNotBlank(dir) ? String.join(StrUtil.SLASH, baseUrl, dir, fileName) : String.join(StrUtil.SLASH, baseUrl, fileName);
	}

    /**
     * 读取文件
     *
     * @param fileName
     * @param response
     */
    @Override
    public void getFile(String fileName, HttpServletResponse response) {
        TenantContextHolder.setTenantSkip();
        SysFileVO sysFile = getFileData(fileName);
        try (S3Object s3Object = fileTemplate.getObject(sysFile.getBucketName(), sysFile.getDir(), fileName)) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            // 文件唯一hash
            response.addHeader("Hash", sysFile.getHash());
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(sysFile.getOriginal()));
            IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
        } catch (Exception e) {
            log.error("文件读取异常: {}", e.getLocalizedMessage());
        }
    }

    @Override
    @CacheEvict(value = CacheConstants.SYS_FILE_INFO, key = "#fileName")
    public SysFileVO getFileData(String fileName) {
        return BeanUtil.toBean(baseMapper.selectOne(Wrappers.<SysFile>lambdaQuery().eq(SysFile::getFileName, fileName)), SysFileVO.class);
    }

	@Override
	@CacheEvict(value = CacheConstants.SYS_FILE_INFO, key = "#remark")
	public List<SysFile> getFileListByRemark(String remark) {
		return baseMapper.selectList(Wrappers.<SysFile>lambdaQuery()
				.eq(SysFile::getRemark, remark).orderByAsc(SysFile::getFileName));
	}

    /**
     * 删除文件
     *
     * @param id
     * @return
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteFile(Long id) {
        SysFile file = this.getById(id);
        if (Objects.isNull(file)) {
            return Boolean.FALSE;
        }
        fileTemplate.removeObject(properties.getBucketName(), file.getFileName());

        // 删除缓存
        Cache cache = cacheManager.getCache(CacheConstants.SYS_FILE_INFO);
        if (cache != null && StrUtil.isNotBlank(file.getFileName())) {
            cache.evict(file.getFileName());
        }

        return this.removeById(id);
    }

    /**
     * 查询文件组列表
     *
     * @param fileGroup SysFileGroup对象，用于筛选条件
     * @return 包含文件组树形结构列表的List对象
     */
    @Override
    public List<Tree<Long>> listFileGroup(SysFileGroup fileGroup) {
        // 从数据库查询文件组列表
        List<TreeNode<Long>> treeNodeList = fileGroupMapper.selectList(Wrappers.query(fileGroup))
                .stream()
                .map(group -> {
                    TreeNode<Long> treeNode = new TreeNode<>();
                    treeNode.setName(group.getName());
                    treeNode.setId(group.getId());
                    treeNode.setParentId(group.getPid());
                    return treeNode;
                })
                .toList();

        // 构建树形结构
        List<Tree<Long>> treeList = TreeUtil.build(treeNodeList, CommonConstants.MENU_TREE_ROOT_ID);
        return CollUtil.isEmpty(treeList) ? new ArrayList<>() : treeList;
    }

    /**
     * 添加或更新文件组
     *
     * @param fileGroup SysFileGroup对象，要添加或更新的文件组信息
     * @return 添加或更新成功返回true，否则返回false
     */
    @Override
    public Boolean saveOrUpdateGroup(SysFileGroup fileGroup) {
        if (Objects.isNull(fileGroup.getId())) {
            // 插入文件组
            fileGroupMapper.insert(fileGroup);
        } else {
            // 更新文件组
            fileGroupMapper.updateById(fileGroup);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除文件组
     *
     * @param id 待删除文件组的ID
     * @return 删除成功返回true，否则返回false
     */
    @Override
    public Boolean deleteGroup(Long id) {
        // 根据ID删除文件组
        fileGroupMapper.deleteById(id);
        return Boolean.TRUE;
    }

    /**
     * 移动文件组
     *
     * @param fileGroupDTO SysFileGroupDTO对象，要移动的文件组信息
     * @return 移动成功返回true，否则返回false
     */
    @Override
    public Boolean moveFileGroup(SysFileGroupDTO fileGroupDTO) {
        // 创建SysFile对象并设置groupId属性
        SysFile file = new SysFile();
        file.setGroupId(fileGroupDTO.getGroupId());

        // 根据IDS更新对应的SysFile记录
        baseMapper.update(file, Wrappers.<SysFile>lambdaQuery().in(SysFile::getId, fileGroupDTO.getIds()));
        return Boolean.TRUE;
    }

    /**
     * 文件管理数据记录，收集管理追踪文件
     *
     * @param file     上传的文件格式
     * @param dir      文件夹
     * @param fileName 文件名
     * @param groupId  文件组ID
     * @param type     文件类型
     */
    @SneakyThrows
    private void fileLog(MultipartFile file, String dir, String fileName, Long groupId, String type) {
        // 创建SysFile对象并设置相关属性
        SysFileLogDTO sysFile = new SysFileLogDTO();
        sysFile.setFileName(fileName);

        // 对原始文件名进行编码转换
        String originalFilename = new String(Objects.requireNonNull(file.getOriginalFilename()).getBytes(StandardCharsets.ISO_8859_1),
                StandardCharsets.UTF_8);
        sysFile.setBucketName(properties.getBucketName());
        sysFile.setHash(SecureUtil.md5(file.getInputStream()));
        sysFile.setOriginal(originalFilename);
        sysFile.setFileSize(file.getSize());
        sysFile.setGroupId(groupId);
        sysFile.setType(type);
        sysFile.setDir(dir);

        // 保存SysFile对象
        saveFileLog(sysFile);
    }

    @Override
    public void saveFileLog(SysFileLogDTO fileLogDTO) {
        // 创建SysFile对象并设置相关属性
        SysFile sysFile = new SysFile();
        sysFile.setBucketName(fileLogDTO.getBucketName());
        sysFile.setFileName(fileLogDTO.getFileName());
        sysFile.setOriginal(fileLogDTO.getOriginal());
        sysFile.setFileSize(fileLogDTO.getFileSize());
        sysFile.setCreateBy(fileLogDTO.getCreateBy());
        sysFile.setUpdateBy(fileLogDTO.getCreateBy());
        sysFile.setGroupId(fileLogDTO.getGroupId());
        sysFile.setHash(fileLogDTO.getHash());
        sysFile.setType(fileLogDTO.getType());
        sysFile.setDir(fileLogDTO.getDir());
		if (StrUtil.isNotBlank(fileLogDTO.getRemark())) {
			sysFile.setRemark(fileLogDTO.getRemark());
		}

        // 文件类型不存在时,根据文件后缀名自动设置文件类型
        String extName = FileUtil.extName(sysFile.getFileName());
        if (StrUtil.isBlank(sysFile.getType()) && StrUtil.isNotBlank(extName)) {
            sysFile.setType(FileTypeEnum.getByExName(extName).getType());
        }

        // 调用save方法保存SysFile对象
        this.save(sysFile);
    }

	@Override
	public Integer removeByNames(Collection<String> fileNames) {
		return baseMapper.delete(new LambdaQueryWrapper<SysFile>().in(SysFile::getFileName, fileNames));
	}

	@Override
	public void updateFile(SysFileVO fileVO) {
		if (ObjUtil.isNull(fileVO.getId())) {
			return;
		}
		SysFile sysFile = new SysFile();
		BeanUtil.copyProperties(fileVO, sysFile);
		baseMapper.updateById(sysFile);
	}

	@Override
	public void batchSave(List<SysFileLogDTO> fileList) {
		if (CollUtil.isEmpty(fileList)) {
			return;
		}
		List<SysFile> sysFiles = fileList.parallelStream().map(item -> {
			SysFile sysFile = new SysFile();
			BeanUtil.copyProperties(item, sysFile);
			sysFile.setUpdateBy(item.getCreateBy());
			return sysFile;
		}).toList();
		this.saveBatch(sysFiles);
	}

    @Override
    public List<SysFile> getFileListByUser(String userName) {
		return baseMapper.selectList(Wrappers.<SysFile>lambdaQuery()
				.eq(SysFile::getCreateBy, userName)
				.and(w->w.likeRight(SysFile::getRemark, FileCompressEnum.WAITDECOMPRESS.getCode())
								.or().likeRight(SysFile::getRemark, FileCompressEnum.DECOMPRESSING.getCode())));
    }
    @Override
    public List<SysFile> getUserFailData(String userName) {
		return baseMapper.selectList(Wrappers.<SysFile>lambdaQuery()
				.eq(SysFile::getCreateBy, userName)
				.likeRight(SysFile::getRemark, FileCompressEnum.DECOMPRESSFAIL.getCode())
				.orderByDesc(SysFile::getId));
    }

}
