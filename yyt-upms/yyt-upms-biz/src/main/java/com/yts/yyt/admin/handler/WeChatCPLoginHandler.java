/*
 *    Copyright (c) 2018-2025, yyt All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: yyt
 */

package com.yts.yyt.admin.handler;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yts.yyt.admin.api.dto.UserInfo;
import com.yts.yyt.admin.api.entity.SysSocialDetails;
import com.yts.yyt.admin.api.entity.SysUser;
import com.yts.yyt.admin.mapper.SysSocialDetailsMapper;
import com.yts.yyt.admin.service.SysUserService;
import com.yts.yyt.common.core.constant.enums.LoginTypeEnum;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-10-14
 * <p>
 * 企业微信登录
 */
@Slf4j
@Component("WEIXIN_CP")
@RequiredArgsConstructor
public class WeChatCPLoginHandler extends AbstractLoginHandler {

    private final SysUserService sysUserService;

    private final SysSocialDetailsMapper sysSocialDetailsMapper;

    /**
     * 企微登录传入code
     * <p>
     * 通过code 调用获取唯一标识
     *
     * @param code
     * @return
     */
    @Override
    public String identify(String code) {
        SysSocialDetails condition = new SysSocialDetails();
        condition.setType(LoginTypeEnum.WEIXIN_CP.getType());
        SysSocialDetails socialDetails = sysSocialDetailsMapper.selectOne(new QueryWrapper<>(condition));

        String getAccessTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

        String accessTokenResult = HttpUtil
                .get(String.format(getAccessTokenUrl, socialDetails.getAppId(), socialDetails.getAppSecret()));
        log.debug("获取企业微信Token响应报文：{}", accessTokenResult);
        String accessToken = JSONUtil.parseObj(accessTokenResult).getStr("access_token");
        String getUserIdUrl = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=%s&code=%s";
        String userResult = HttpUtil.get(String.format(getUserIdUrl, accessToken, code));
        log.debug("获取企业微信UserId响应报文:{}", userResult);
        // 企微唯一标识
        return JSONUtil.parseObj(userResult).getStr("userid");
    }

    /**
     * openId 获取用户信息
     *
     * @param openId
     * @return
     */
    @Override
    public UserInfo info(String openId) {
        SysUser user = sysUserService.getOne(Wrappers.<SysUser>query().lambda().eq(SysUser::getWxCpUserid, openId));

        if (user == null) {
            log.info("企业微信未绑定:{}", openId);
            return null;
        }
        return sysUserService.findUserInfo(user);
    }

    /**
     * 绑定逻辑
     *
     * @param user     用户实体
     * @param identify 渠道返回唯一标识
     * @return
     */
    @Override
    public Boolean bind(SysUser user, String identify) {
        user.setWxCpUserid(identify);
        sysUserService.updateById(user);
        return true;
    }

}
