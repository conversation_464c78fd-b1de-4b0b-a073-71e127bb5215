<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, yyt All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: yyt
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yts</groupId>
        <artifactId>yyt-upms</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>yyt-upms-biz</artifactId>
    <packaging>jar</packaging>

    <description>yyt 通用用户权限管理系统业务处理模块</description>

    <dependencies>
        <!-- mysql -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- ojdbc8 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!--PG-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!--mssql-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <!--DM8-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-upms-api</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-seata</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-swagger</artifactId>
        </dependency>
        <!--文件系统-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-oss</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--spring security 、oauth、jwt依赖-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-security</artifactId>
        </dependency>
        <!--XSS 安全过滤-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-xss</artifactId>
        </dependency>
        <!-- 字段审计 -->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-audit</artifactId>
        </dependency>
        <!--支持动态路由配置 -->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-gateway</artifactId>
        </dependency>
        <!--sentinel 依赖-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-sentinel</artifactId>
        </dependency>
        <!--路由控制-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-gray</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-seata</artifactId>
        </dependency>

        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <!-- cas sdk -->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>${cas.sdk.version}</version>
        </dependency>
        <!--旧版api,新版api未包含全部的服务端API的产品能力-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>${dingtalk.old.version}</version>
        </dependency>
        <!--加密类-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
        <!--企业微信-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>
        <!-- 短信工具类 -->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-spring-boot-starter</artifactId>
        </dependency>
        <!-- 邮件工具 -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
        </dependency>
        <!-- webhook工具类 -->
        <dependency>
            <groupId>org.dromara.sms4j</groupId>
            <artifactId>sms4j-oa-core</artifactId>
        </dependency>
        <!-- 模板引擎-->
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <loaderImplementation>CLASSIC</loaderImplementation>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
