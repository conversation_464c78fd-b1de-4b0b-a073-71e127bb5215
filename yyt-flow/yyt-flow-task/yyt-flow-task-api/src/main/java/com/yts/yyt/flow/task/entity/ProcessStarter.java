package com.yts.yyt.flow.task.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.yts.yyt.common.core.util.TenantTable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 流程发起人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Getter
@Setter
@TenantTable
@Accessors(chain = true)
@TableName("process_starter")
public class ProcessStarter {

	/**
	 * 用户id或者部门id
	 */
	@TableField("type_id")
	private Long typeId;

	/**
	 * 类型 user dept
	 */
	@TableField("type")
	private String type;

	/**
	 * 流程id
	 */
	@TableField("process_id")
	private Long processId;

	/**
	 * 用户id
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 逻辑删除字段
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	private String delFlag;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	private LocalDateTime updateTime;

}
