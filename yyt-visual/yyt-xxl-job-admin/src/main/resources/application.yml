# 此配置只适合开发测试环境，详细配置参考： http://t.cn/A64RaHJm
server:
  port: 9080
  servlet:
    context-path: /xxl-job-admin

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: @nacos.ip@
        namespace: @profiles.active@
        metadata:
          management.context-path: ${server.servlet.context-path}/actuator
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: @profiles.active@
  config:
    import:
      - optional:nacos:application.yml
      - optional:nacos:${spring.application.name}.yml
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: ${MYSQL_USER:yyt}
      password: ${MYSQL_PWD:5B1f1BWlAZ.X5hy}
      url: **********************@/${MYSQL_DB:yytx_job}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true
  mvc:
    static-path-pattern: /static/**
  freemarker:
    suffix: .ftl
    request-context-attribute: request
    settings:
      number_format: 0.##########
    template-loader-path: classpath:/templates/
  mail:
    host: smtp.mxhichina.com
    port: 465
    from: <EMAIL>
    username: <EMAIL>
    password: xxxx
    properties:
      mail:
        smtp:
          auth: true
          ssl.enable: true
          starttls.enable: false
          required: false
xxl:
  job:
    accessToken:
    i18n: zh_CN
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
    logretentiondays: 30

mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
