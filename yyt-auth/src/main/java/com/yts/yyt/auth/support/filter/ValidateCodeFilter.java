package com.yts.yyt.auth.support.filter;

/**
 * 登录前处理器
 *
 * <AUTHOR>
 * @date 2024/4/3
 */

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.yts.yyt.auth.config.IosReviewConfig;
import com.yts.yyt.common.core.constant.CacheConstants;
import com.yts.yyt.common.core.constant.CommonConstants;
import com.yts.yyt.common.core.constant.SecurityConstants;
import com.yts.yyt.common.core.constant.enums.CaptchaFlagTypeEnum;
import com.yts.yyt.common.core.exception.ValidateCodeException;
import com.yts.yyt.common.core.util.SpringContextHolder;
import com.yts.yyt.common.core.util.WebUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-01-06
 * <p>
 * 登录前置处理器： 前端密码传输密文解密，验证码处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ValidateCodeFilter extends OncePerRequestFilter {


    private final StringRedisTemplate redisTemplate;
	private final IosReviewConfig iosReviewConfig;
    private final Environment env;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestUrl = request.getServletPath();

        // 不是登录URL 请求直接跳过
        if (!SecurityConstants.OAUTH_TOKEN_URL.equals(requestUrl)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 如果登录URL 但是刷新token的请求，直接向下执行
        String grantType = request.getParameter(OAuth2ParameterNames.GRANT_TYPE);
        if (StrUtil.containsAny(grantType, SecurityConstants.REFRESH_TOKEN)) {
            filterChain.doFilter(request, response);
            return;
        }

        // mobile模式, 如果请求不包含mobile 参数直接
        String mobile = request.getParameter(SecurityConstants.GRANT_MOBILE);
        if (StrUtil.equals(SecurityConstants.GRANT_MOBILE, grantType) && StrUtil.isBlank(mobile)) {
            throw new OAuth2AuthenticationException(SecurityConstants.GRANT_MOBILE);
        }

        // mobile模式, 社交登录模式不校验验证码直接跳过
        if (StrUtil.equals(SecurityConstants.GRANT_MOBILE, grantType) && !StrUtil.contains(mobile, "SMS")) {
            filterChain.doFilter(request, response);
            return;
        }

        // 判断客户端是否跳过检验
        if (!isCheckCaptchaClient(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 校验验证码 1. 客户端开启验证码 2. 短信模式
        try {
            checkCode();
            filterChain.doFilter(request, response);
        } catch (ValidateCodeException validateCodeException) {
            throw new OAuth2AuthenticationException(validateCodeException.getMessage());
        }
    }

    /**
     * 校验验证码
     */
    private void checkCode() throws ValidateCodeException {
        HttpServletRequest request = WebUtils.getRequest();
        String code = request.getParameter("code");

        if (StrUtil.isBlank(code)) {
            throw new ValidateCodeException("验证码不能为空");
        }

        String randomStr = request.getParameter("randomStr");

        // https://gitee.com/log4j/pig/issues/IWA0D
        String mobile = request.getParameter("mobile");
        if (StrUtil.isNotBlank(mobile)) {
            randomStr = mobile;
        }

        // 若是滑块登录
        if (CommonConstants.IMAGE_CODE_TYPE.equalsIgnoreCase(randomStr)) {
            CaptchaService captchaService = SpringContextHolder.getBean(CaptchaService.class);
            CaptchaVO vo = new CaptchaVO();
            vo.setCaptchaVerification(code);
            vo.setCaptchaType(CommonConstants.IMAGE_CODE_TYPE);
            if (!captchaService.verification(vo).isSuccess()) {
                throw new ValidateCodeException("验证码不能为空");
            }
            return;
        }
        String[] profiles = env.getActiveProfiles();
        if(profiles != null && profiles.length >0) {
            log.info("当前环境:{}", profiles[0]);
            if(("sit".equals(profiles[0]) ||  "dev".equals(profiles[0])) && "1234".equals(code)) {
                return;
            }
        }
        // 根据开关是否开启来校验手机号和固定验证码
        log.info("IosReviewConfig:是否开启Ios提审验证码开关:{}", iosReviewConfig.isSwitchFlag());
        if (iosReviewConfig.isSwitchFlag()) {
            // log.info("IosReviewConfig:开始校验:{}", JSONUtil.toJsonStr(iosReviewConfig));
            log.info("IosReviewConfig:开始校验:switchFlag={}, checkMobile={}, code={}", 
                                                            iosReviewConfig.isSwitchFlag(),
                                                            iosReviewConfig.getCheckMobile(),
                                                            iosReviewConfig.getCode());
            String checkMobile = iosReviewConfig.getCheckMobile();
            String checkCode = iosReviewConfig.getCode();

            // 校验手机号和验证码
            if (StrUtil.isBlank(checkMobile) || StrUtil.isBlank(checkCode)) {
                throw new ValidateCodeException("配置的手机号或验证码不能为空");
            }

            if (extractMobileNumber(mobile).equals(checkMobile)) {
                // 针对该手机号和验证码单独处理,不做真实验证码校验
                if (!StrUtil.equals(checkCode, code)) {
                    log.info("IosReviewConfig:校验失败:{}|{}|{}|{}", checkMobile, mobile, checkCode, code);
                    throw new ValidateCodeException("固定验证码校验失败，请重新输入");
                }
                log.info("IosReviewConfig:校验正确");
                return; // 校验通过，直接返回
            }
        }

		
        String key = CacheConstants.DEFAULT_CODE_KEY + randomStr;
        if (Boolean.FALSE.equals(redisTemplate.hasKey(key))) {
            throw new ValidateCodeException("验证码不合法");
        }

        Object codeObj = redisTemplate.opsForValue().get(key);

        if (codeObj == null) {
            throw new ValidateCodeException("验证码不合法");
        }

        String saveCode = codeObj.toString();
        if (StrUtil.isBlank(saveCode)) {
            redisTemplate.delete(key);
            throw new ValidateCodeException("验证码不合法");
        }

        if (!StrUtil.equals(saveCode, code)) {
//            redisTemplate.delete(key);
            throw new ValidateCodeException("验证码不合法");
        }

        redisTemplate.delete(key);
    }

	/**
	 * mobile: MOBILE-SMS-APP@15517589670
	 * 需要15517589670
	 * 定义正则表达式，获取 @ 后面的连续数字
	 */
	private String extractMobileNumber(String input) {
		String regex = "@(\\d+)";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(input);
		if (matcher.find()) {
			// 提取匹配到的手机号
			return matcher.group(1);
		}
		return "";
	}

    /**
     * 是否需要校验客户端，根据client 查询客户端配置
     *
     * @param request 请求
     * @return true 需要校验， false 不需要校验
     */
    private boolean isCheckCaptchaClient(HttpServletRequest request) {
        String header = request.getHeader(HttpHeaders.AUTHORIZATION);
        String clientId = WebUtils.extractClientId(header).orElse(null);
        // 获取租户拼接区分租户的key
        String tenantId = request.getHeader(CommonConstants.TENANT_ID);
        String key = String.format("%s:%s:%s", StrUtil.isBlank(tenantId) ? CommonConstants.TENANT_ID_1 : tenantId,
                CacheConstants.CLIENT_FLAG, clientId);

        String val = redisTemplate.opsForValue().get(key);

        // 当配置不存在时，不用校验
        if (val == null) {
            return false;
        }

        JSONObject information = JSONUtil.parseObj(val);
        return !StrUtil.equals(CaptchaFlagTypeEnum.OFF.getType(), information.getStr(CommonConstants.CAPTCHA_FLAG));
    }
}
