package com.yts.yyt.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "ios.review.configuration")
public class IosReviewConfig {

	// 开关参数
	private boolean switchFlag = false;
	// 账号参数
	private String checkMobile;
	// 固定验证码参数
	private String code;

}