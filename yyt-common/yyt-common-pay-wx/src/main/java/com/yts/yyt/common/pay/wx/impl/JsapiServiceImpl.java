package com.yts.yyt.common.pay.wx.impl;


import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.yts.yyt.common.pay.wx.WxJsapiService;
import com.yts.yyt.common.pay.wx.properties.JsapiProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <p>2025-01-07</p>
 * <p>JSAPI.</p>
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class JsapiServiceImpl implements WxJsapiService {

    private final JsapiService jsapiService;

    @Override
    public String prepay (PrepayRequest request) {
        PrepayResponse response = jsapiService.prepay(request);
        return response.getPrepayId();
    }

    @Override
    public Transaction queryOrderById (String mchid, String transactionId) {
        QueryOrderByIdRequest request = new QueryOrderByIdRequest();
        request.setMchid(mchid);
        request.setTransactionId(transactionId);
        return jsapiService.queryOrderById(request);
    }

    @Override
    public Transaction queryOrderByOutTradeNo (String mchid, String outTradeNo) {
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(mchid);
        request.setOutTradeNo(outTradeNo);
        return jsapiService.queryOrderByOutTradeNo(request);
    }

    @Override
    public void closeOrder (String mchid, String outTradeNo) {
        CloseOrderRequest request = new CloseOrderRequest();
        request.setMchid(mchid);
        request.setOutTradeNo(outTradeNo);
        jsapiService.closeOrder(request);
    }
}
