package com.yts.yyt.common.pay.wx;

import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.model.Transaction;

/**
 * <p>2025-01-07</p>
 * <p>JSAPI.</p>
 *
 * <AUTHOR>
 */
public interface WxJsapiService {

    /**
     * 预支付
     * @see <a href="https://pay.weixin.qq.com/doc/v3/merchant/4012791897">参数文档</a>
     * @return prepayId 说明：预支付交易会话标识
     */
    String prepay (PrepayRequest request);

    /**
     * 微信支付订单号查询订单
     *
     * @param transactionId 交易id
     * @return Transaction
     */
    Transaction queryOrderById(String mchid, String transactionId);

    /**
     * 商户订单号查询订单
     *
     * @param outTradeNo 订单编号
     * @return Transaction
     */
    Transaction queryOrderByOutTradeNo(String mchid, String outTradeNo);

    /**
     * 关闭订单
     * @param outTradeNo 订单号
     */
    void closeOrder(String mchid, String outTradeNo);
}
