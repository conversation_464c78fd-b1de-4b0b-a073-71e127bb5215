package com.yts.yyt.common.pay.wx.config;

import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.yts.yyt.common.pay.wx.properties.JsapiProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * <p>2025-01-07</p>
 * <p>jsapi配置.</p>
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class JsapiConfig {

    private final JsapiProperties properties;

    @Bean
    @ConditionalOnMissingBean
    public RSAAutoCertificateConfig weChatPayConfig() {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(properties.getMerchantId())
                .privateKeyFromPath(properties.getPrivateKeyPath())
                .merchantSerialNumber(properties.getMerchantSerialNumber())
                .apiV3Key(properties.getApiV3Key())
                .build();
    }

    @Bean
    public JsapiService jsapiService(RSAAutoCertificateConfig config) {
        return new JsapiService.Builder().config(config).build();
    }

    @Bean
    public NotificationParser notificationParser(RSAAutoCertificateConfig config) {
        return new NotificationParser(config);
    }
}
