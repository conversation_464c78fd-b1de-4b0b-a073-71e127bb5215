package com.yts.yyt.common.pay.wx.properties;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;

/**
 * <p>2025-01-07</p>
 * <p>jsapi配置.</p>
 *
 * <AUTHOR>
 */
@Getter
//@PropertySource(value = "classpath:/application-jsapi.properties")
public class JsapiProperties {

    @Value("${wx-jsapi-merchant-id}")
    private String merchantId;

    @Value("${wx-jsapi-merchant-serial-number}")
    private String merchantSerialNumber;

    @Value("${wx-jsapi-api-v3-key}")
    private String apiV3Key;

    @Value("${wx-jsapi-notify-url}")
    private String notifyUrl;

    @Value("${wx-jsapi-private-key-path}")
    private String privateKeyPath;
}
