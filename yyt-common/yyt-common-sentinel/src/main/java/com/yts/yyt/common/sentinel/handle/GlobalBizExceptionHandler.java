/*
 *    Copyright (c) 2018-2025, yyt All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: yyt
 */

package com.yts.yyt.common.sentinel.handle;

import com.alibaba.csp.sentinel.Tracer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yts.yyt.common.core.exception.GlobalBizException;
import com.yts.yyt.common.core.exception.ParamsValidateException;
import com.yts.yyt.common.core.util.R;
import feign.FeignException;
import jakarta.validation.ConstraintViolationException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-29
 */
@Slf4j
@RestControllerAdvice
public class GlobalBizExceptionHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 是否为开发环境，开发环境显示详细错误信息
     */
    @Value("${spring.profiles.active:prod}")
    private String activeProfile;

    /**
     * 错误消息常量
     */
    private static final String SYSTEM_ERROR_MSG = "系统繁忙，请稍后重试";
    private static final String DATABASE_ERROR_MSG = "数据库调用异常，请联系管理员处理";
    private static final String FEIGN_ERROR_MSG = "服务调用异常，请稍后重试";
    private static final String ACCESS_DENIED_MSG = "权限不足，不允许访问";
    private static final String NOT_FOUND_MSG = "请求的资源不存在";

    /**
     * 判断是否为开发环境
     */
    private boolean isDevelopment() {
        return "local".equals(activeProfile) || "sit".equals(activeProfile);
    }

    /**
     * 获取用户友好的错误消息
     * @param defaultMsg 默认消息
     * @param detailMsg 详细消息（仅开发环境显示）
     * @return 最终显示给用户的消息
     */
    private String getUserFriendlyMessage(String defaultMsg, String detailMsg) {
        return isDevelopment() ? detailMsg : defaultMsg;
    }

    /**
     * 全局异常.
     *
     * @param e the e
     * @return R
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleGlobalException(Exception e) {

        log.error("全局异常信息 ex={}", e.getMessage(), e);

        // 业务异常交由 sentinel 记录
        Tracer.trace(e);

        // 返回用户友好的错误消息，避免暴露系统内部信息
        String userMessage = getUserFriendlyMessage(SYSTEM_ERROR_MSG, e.getLocalizedMessage());
        return R.failed(userMessage);
    }

    /**
     * SQLException Exception
     *
     * @param exception 数据库调用异常
     * @return R
     */
    @ExceptionHandler({SQLException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleSQLException(SQLException exception) {
        log.error("数据库调用异常 ex={}", exception.getMessage(), exception);

        // 数据库异常不暴露具体错误信息给前端
        String userMessage = getUserFriendlyMessage(DATABASE_ERROR_MSG, exception.getMessage());
        return R.failed(userMessage);
    }

    @SneakyThrows
    @ExceptionHandler(FeignException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleGlobalException(FeignException e) {
        log.error("Feign调用异常 ex={}", e.getMessage(), e);

        // 业务异常交由 sentinel 记录
        Tracer.trace(e);

        // 如果有响应体，尝试解析返回
        if (e.responseBody().isPresent()) {
            try {
                return objectMapper.readValue(e.responseBody().get().array(), R.class);
            } catch (Exception parseException) {
                log.warn("解析Feign响应体失败: {}", parseException.getMessage());
            }
        }

        // 返回用户友好的错误消息
        String userMessage = getUserFriendlyMessage(FEIGN_ERROR_MSG, e.getLocalizedMessage());
        return R.failed(userMessage);
    }

    /**
     * AccessDeniedException
     *
     * @param e the e
     * @return R
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public R handleAccessDeniedException(AccessDeniedException e) {
        log.error("拒绝授权异常信息 ex={}", e.getMessage());

        // 权限异常使用固定的用户友好消息
        return R.failed(ACCESS_DENIED_MSG);
    }

    /**
     * validation Exception
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleBodyValidException(BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        // 插入log 的逻辑
        return R.failed(String.format("%s %s", fieldErrors.get(0).getField(), fieldErrors.get(0).getDefaultMessage()));
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class, ConstraintViolationException.class })
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleFastFailValidExceptions(Exception ex) {
        log.info("参数校验异常，exceptionName={}, message={}", ex.getClass().getName(), ex.getMessage());
        if (ex instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validEx = (MethodArgumentNotValidException) ex;
            List<ObjectError> errors = validEx.getBindingResult().getAllErrors();
            String defaultMessage = errors.get(0).getDefaultMessage();
            if (defaultMessage == null) {
                return handleGlobalException(validEx);
            }
            String[] array = defaultMessage.split("\\|");
            if (array != null && array.length !=2 ) {
                if(array.length == 1)
                    return R.failed(null, array[0]);
                return handleGlobalException(validEx);
            }
            return R.failed(null, array[1]);
        } else if (ex instanceof ConstraintViolationException) {
            ConstraintViolationException constraintEx = (ConstraintViolationException) ex;
            String defaultMessage = constraintEx.getMessage();
            if (defaultMessage == null) {
                return handleGlobalException(constraintEx);
            }
            String[] split = defaultMessage.split(":");
            if(split != null && split.length != 2) {
                return handleGlobalException(constraintEx);
            }
            String message  = split[1];
            String[] array = message.split("\\|");

            if (array.length  > 2 ) {
                return handleGlobalException(constraintEx);
            }else if (array.length == 2) {
                return R.failed(null, array[1]);
            } else {
                return R.failed(message);
            }
        } else {
            return R.failed(HttpStatus.EXPECTATION_FAILED.getReasonPhrase());
        }
    }

    /**
     * 避免 404 重定向到 /error 导致NPE ,ignore-url 需要配置对应端点
     *
     * @return R
     */
    @DeleteMapping("/error")
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public R noHandlerFoundException() {
        return R.failed(HttpStatus.NOT_FOUND.getReasonPhrase());
    }

    /**
     * 保持和低版本请求路径不存在的行为一致
     * <p>
     * <a href="https://github.com/spring-projects/spring-boot/issues/38733">[Spring Boot
     * 3.2.0] 404 Not Found behavior #38733</a>
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({NoResourceFoundException.class})
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public R noResourceFoundException(NoResourceFoundException exception) {
        log.debug("请求路径 404 {}", exception.getMessage());

        // 404异常返回用户友好的消息，避免暴露路径信息
        String userMessage = getUserFriendlyMessage(NOT_FOUND_MSG, exception.getMessage());
        return R.failed(userMessage);
    }


    /**
     * 业务异常
     * @param e
     * @return R
     */
    @ExceptionHandler({ GlobalBizException.class })
    @ResponseStatus(HttpStatus.OK)
    public R serviceExceptionHandler(GlobalBizException e) {
        log.error("全局异常信息业务异常 ex={}", e.getMessage());

        log.error("全局异常信息业务异常，e:{}", e);
        // 业务异常交由 sentinel 记录
        Tracer.trace(e);

        return R.restResult(null, e.getCode(), e.getMsg());
    }
    /**
     * 业务异常
     * @param e
     * @return R
     */
    @ExceptionHandler({ ParamsValidateException.class })
    @ResponseStatus(HttpStatus.OK)
    public R serviceExceptionHandler(ParamsValidateException e) {
        log.error("全局异常信息参数校验异常 ex={}", e.getMessage());

        log.error("全局异常信息参数校验异常，e:{}", e);
        // 业务异常交由 sentinel 记录
        Tracer.trace(e);

        return R.restResult(null, e.getCode(), e.getMsg());
    }
}
