package com.yts.yyt.common.logistics.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;

/**
 * <p>2025-01-07</p>
 * <p>物流配置.</p>
 *
 * <AUTHOR>
 */
@Getter
//@PropertySource(value = "classpath:/application-logistics.properties")
public class LogisticsProperties {

    @Value("${logistics-key}")
    private String key;

    @Value("${logistics-customer}")
    private String customer;

    @Value("${logistics-secret}")
    private String secret;

    @Value("${logistics-callback-url}")
    private String callbackUrl;

    @Value("${logistics-callback-salt}")
    private String callbackSalt;
}
