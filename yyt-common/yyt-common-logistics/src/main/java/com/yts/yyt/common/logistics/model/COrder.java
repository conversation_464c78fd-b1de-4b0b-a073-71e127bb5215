package com.yts.yyt.common.logistics.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>2025-02-24</p>
 * <p>C端寄件下单.</p>
 *
 * <AUTHOR>
 */
@Data
public class COrder implements Serializable {

    private String kuaidicom;
    private String recManName;
    private String recManMobile;
    private String recManPrintAddr;
    private String sendManName;
    private String sendManMobile;
    private String sendManPrintAddr;
    private String callBackUrl;
    private String cargo;
    private String payment;
    private String weight;
    private String remark;
    private String dayType;
    private String pickupStartTime;
    private String pickupEndTime;
    private String salt;
    private String op;
    private String pollCallBackUrl;
    private String resultv2;
    private String thirdOrderId;
}
