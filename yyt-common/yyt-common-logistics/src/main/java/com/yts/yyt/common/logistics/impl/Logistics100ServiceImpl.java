package com.yts.yyt.common.logistics.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.kuaidi100.sdk.api.AutoNum;
import com.kuaidi100.sdk.api.COrder;
import com.kuaidi100.sdk.api.QueryTrack;
import com.kuaidi100.sdk.api.Subscribe;
import com.kuaidi100.sdk.contant.ApiInfoConstant;
import com.kuaidi100.sdk.contant.CompanyConstant;
import com.kuaidi100.sdk.core.IBaseClient;
import com.kuaidi100.sdk.request.*;
import com.kuaidi100.sdk.request.cloud.COrderCancelReq;
import com.kuaidi100.sdk.request.corder.COrderQueryPriceReq;
import com.kuaidi100.sdk.request.corder.COrderReq;
import com.kuaidi100.sdk.utils.SignUtils;
import com.yts.yyt.common.logistics.LogisticsService;
import com.yts.yyt.common.logistics.config.LogisticsProperties;
import com.yts.yyt.common.logistics.model.COrderCancelResp;
import com.yts.yyt.common.logistics.model.COrderQueryPriceResp;
import com.yts.yyt.common.logistics.model.COrderResp;
import com.yts.yyt.common.logistics.model.ExpressCompany;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>2025-01-07</p>
 * <p>100 平台实现.</p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class Logistics100ServiceImpl implements LogisticsService {

    private final LogisticsProperties logisticsProperties;

    @Override
    public String query (String com, String num, String phone) {
        String key = logisticsProperties.getKey();
        String customer = logisticsProperties.getCustomer();
        QueryTrackReq queryTrackReq = new QueryTrackReq();
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(com);
        queryTrackParam.setNum(num);
        queryTrackParam.setPhone(phone);
        queryTrackParam.setResultv2("4");
        String param = new Gson().toJson(queryTrackParam);

        queryTrackReq.setParam(param);
        queryTrackReq.setCustomer(customer);
        queryTrackReq.setSign(SignUtils.querySign(param ,key,customer));

        IBaseClient baseClient = new QueryTrack();
        try {
            return baseClient.execute(queryTrackReq).getBody();
        } catch (Exception e) {
            log.error("【快递100】", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ExpressCompany autonumber (String num) {
		log.info("【autonumber】快递单号：{}",num);
        String key = logisticsProperties.getKey();
        AutoNumReq autoNumReq = new AutoNumReq();
        autoNumReq.setKey(key);
        autoNumReq.setNum(num);
        IBaseClient baseClient = new AutoNum();
        try {
            String autonumber = baseClient.execute(autoNumReq).getBody();
			log.info("【autonumber】resp：{}",autonumber);
            JSONArray array = JSONUtil.parseArray(autonumber);
//            Assert.isFalse(array.isEmpty(), "快递单号不正确");
			if(array.isEmpty()){
				return new ExpressCompany();
			}
            return JSONUtil.toBean(array.get(0).toString(), ExpressCompany.class);
        } catch (Exception e) {
            log.error("【快递100】", e);
            throw new RuntimeException(e);
        }
    }

    public String subscribe(String phone, String company, String number) throws Exception {
        String key = logisticsProperties.getKey();
        String callbackUrl = logisticsProperties.getCallbackUrl();
        String salt = logisticsProperties.getCallbackSalt();
        SubscribeParameters subscribeParameters = new SubscribeParameters();
        subscribeParameters.setCallbackurl(callbackUrl);
        subscribeParameters.setPhone(phone);
        subscribeParameters.setSalt(salt);
        subscribeParameters.setResultv2("4");

        SubscribeParam subscribeParam = new SubscribeParam();
        subscribeParam.setParameters(subscribeParameters);
        subscribeParam.setCompany(company);
        subscribeParam.setNumber(number);
        subscribeParam.setKey(key);

        SubscribeReq subscribeReq = new SubscribeReq();
        subscribeReq.setSchema(ApiInfoConstant.SUBSCRIBE_SCHEMA);
        subscribeReq.setParam(new Gson().toJson(subscribeParam));

        IBaseClient subscribe = new Subscribe();
        return subscribe.execute(subscribeReq).getBody();
    }

    @Override
    public COrderResp cOrder (COrderReq cOrderReq) throws Exception {
        String key = logisticsProperties.getKey();
        String secret = logisticsProperties.getSecret();
		//报没有回调地址异常，故加上
		String callbackUrl = logisticsProperties.getCallbackUrl();
		String salt = logisticsProperties.getCallbackSalt();
		cOrderReq.setCallBackUrl(callbackUrl);
		cOrderReq.setSalt(salt);
		PrintReq printReq = new PrintReq();
		String t = String.valueOf(System.currentTimeMillis());
		String param = new Gson().toJson(cOrderReq);

		printReq.setKey(key);
		printReq.setSign(SignUtils.printSign(param, t, key, secret));
		printReq.setT(t);
		printReq.setParam(param);
		printReq.setMethod(ApiInfoConstant.C_ORDER_METHOD);

        try {
            String order = new COrder().execute(printReq).getBody();
			log.info("【Logistics100ServiceImpl.corder】resp：{}", order);
            return JSONUtil.toBean(order, COrderResp.class);
        } catch (Exception e) {
            log.error("【快递100】", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public COrderCancelResp cOrderCancel (String taskId, String orderId, String cancelMsg) {
        String key = logisticsProperties.getKey();
        String secret = logisticsProperties.getSecret();

        PrintReq printReq = new PrintReq();
        COrderCancelReq cOrderCancelReq = new COrderCancelReq();
        cOrderCancelReq.setTaskId(taskId);
        cOrderCancelReq.setOrderId(orderId);
        cOrderCancelReq.setCancelMsg(cancelMsg);

        String t = String.valueOf(System.currentTimeMillis());
        String param = new Gson().toJson(cOrderCancelReq);

        printReq.setKey(key);
        printReq.setSign(SignUtils.printSign(param,t,key,secret));
        printReq.setT(t);
        printReq.setParam(param);
        printReq.setMethod(ApiInfoConstant.CANCEL_METHOD);

        try {
            String order = new COrder().execute(printReq).getBody();
            return JSONUtil.toBean(order, COrderCancelResp.class);
        } catch (Exception e) {
            log.error("【快递100】", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public COrderQueryPriceResp cOrderQueryPrice (String sendManPrintAddr, String recManPrintAddr) {
        String key = logisticsProperties.getKey();
        String secret = logisticsProperties.getSecret();
        PrintReq printReq = new PrintReq();
        COrderQueryPriceReq queryPriceReq = new COrderQueryPriceReq();
        queryPriceReq.setKuaidicom(CompanyConstant.SF);
        queryPriceReq.setSendManPrintAddr(sendManPrintAddr);
        queryPriceReq.setRecManPrintAddr(recManPrintAddr);

        String t = String.valueOf(System.currentTimeMillis());
        String param = new Gson().toJson(queryPriceReq);

        printReq.setKey(key);
        printReq.setSign(SignUtils.printSign(param,t,key,secret));
        printReq.setT(t);
        printReq.setParam(param);
        printReq.setMethod(ApiInfoConstant.C_ORDER_PRICE_METHOD);

        try {
            String order = new COrder().execute(printReq).getBody();
            return JSONUtil.toBean(order, COrderQueryPriceResp.class);
        } catch (Exception e) {
            log.error("【快递100】", e);
            throw new RuntimeException(e);
        }
    }
}
