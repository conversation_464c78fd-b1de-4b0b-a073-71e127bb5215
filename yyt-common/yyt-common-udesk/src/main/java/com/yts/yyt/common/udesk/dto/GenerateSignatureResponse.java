package com.yts.yyt.common.udesk.dto;

import lombok.Data;

/**
 * 生成WebIM signature响应结果
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class GenerateSignatureResponse {

    /**
     * 生成的signature签名字符串
     */
    private String signature;

    /**
     * 随机数（原样返回）
     */
    private String nonce;

    /**
     * 时间戳（原样返回）
     */
    private String timestamp;

    /**
     * 客户标识（原样返回）
     */
    private String webToken;

    /**
     * 使用的加密算法
     */
    private String encryptionAlgorithm;

    /**
     * 构造函数
     */
    public GenerateSignatureResponse() {
    }

    /**
     * 构造函数
     *
     * @param signature 签名字符串
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @param webToken 客户标识
     * @param encryptionAlgorithm 加密算法
     */
    public GenerateSignatureResponse(String signature, String nonce, String timestamp, 
                                   String webToken, String encryptionAlgorithm) {
        this.signature = signature;
        this.nonce = nonce;
        this.timestamp = timestamp;
        this.webToken = webToken;
        this.encryptionAlgorithm = encryptionAlgorithm;
    }
}
