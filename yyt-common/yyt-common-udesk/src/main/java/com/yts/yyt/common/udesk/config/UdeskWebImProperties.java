package com.yts.yyt.common.udesk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Udesk WebIM配置属性
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "udesk.webim")
public class UdeskWebImProperties {

    /**
     * WebIM用户识别密钥
     * 获取位置：管理中心-即时通讯-网页插件-管理/添加客户信息中的KEY
     */
    private String imUserKey;
}
