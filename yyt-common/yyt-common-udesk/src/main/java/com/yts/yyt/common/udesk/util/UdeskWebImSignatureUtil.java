package com.yts.yyt.common.udesk.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Udesk WebIM Signature生成工具类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
public class UdeskWebImSignatureUtil {

    /**
     * 加密算法枚举
     */
    public enum EncryptionAlgorithm {
        SHA1("SHA1"),
        SHA256("SHA256");

        private final String value;

        EncryptionAlgorithm(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Signature生成结果
     */
    @Data
    public static class SignatureResult {
        private String signature;
        private String nonce;
        private String timestamp;
        private String webToken;
        private String encryptionAlgorithm;

        public SignatureResult(String signature, String nonce, String timestamp, String webToken, String encryptionAlgorithm) {
            this.signature = signature;
            this.nonce = nonce;
            this.timestamp = timestamp;
            this.webToken = webToken;
            this.encryptionAlgorithm = encryptionAlgorithm;
        }
    }

    /**
     * 生成signature签名（简化版本，内部生成nonce和timestamp）
     *
     * @param webToken 客户唯一标识
     * @param imUserKey WebIM用户识别密钥
     * @return 包含signature、nonce、timestamp的结果对象
     */
    public static SignatureResult generateSignature(String webToken, String imUserKey) {
        return generateSignature(webToken, imUserKey, EncryptionAlgorithm.SHA256);
    }

    /**
     * 生成signature签名（简化版本，内部生成nonce和timestamp）
     *
     * @param webToken 客户唯一标识
     * @param imUserKey WebIM用户识别密钥
     * @param algorithm 加密算法
     * @return 包含signature、nonce、timestamp的结果对象
     */
    public static SignatureResult generateSignature(String webToken, String imUserKey, EncryptionAlgorithm algorithm) {
        // 内部生成nonce和timestamp
        String nonce = IdUtil.fastSimpleUUID().substring(0, 16); // 生成16位随机字符串
        String timestamp = String.valueOf(System.currentTimeMillis()); // 当前时间戳

        // 调用内部方法生成signature
        String signature = generateSignatureInternal(nonce, timestamp, webToken, imUserKey, algorithm);

        return new SignatureResult(signature, nonce, timestamp, webToken, algorithm.getValue());
    }

    /**
     * 生成signature签名（内部方法）
     *
     * @param nonce 随机数
     * @param timestamp 时间戳(13位毫秒)
     * @param webToken 客户唯一标识
     * @param imUserKey WebIM用户识别密钥
     * @param algorithm 加密算法
     * @return signature签名字符串
     */
    private static String generateSignatureInternal(String nonce, String timestamp, String webToken,
                                                   String imUserKey, EncryptionAlgorithm algorithm) {
        try {
            // 参数验证
            if (StrUtil.isBlank(nonce)) {
                throw new IllegalArgumentException("nonce不能为空");
            }
            if (StrUtil.isBlank(timestamp)) {
                throw new IllegalArgumentException("timestamp不能为空");
            }
            if (StrUtil.isBlank(webToken)) {
                throw new IllegalArgumentException("webToken不能为空");
            }
            if (StrUtil.isBlank(imUserKey)) {
                throw new IllegalArgumentException("imUserKey不能为空");
            }

            // 第一步：按参数及顺序拼接字符，以key=value&形式
            // 顺序：nonce、timestamp、web_token、im_user_key
            String signStr = String.format("nonce=%s&timestamp=%s&web_token=%s&%s", 
                                          nonce, timestamp, webToken, imUserKey);
            
            log.debug("拼接字符串：{}", signStr);

            // 第二步：使用加密算法计算出签名字符串
            String hashedStr;
            if (algorithm == EncryptionAlgorithm.SHA256) {
                hashedStr = DigestUtil.sha256Hex(signStr);
            } else {
                hashedStr = DigestUtil.sha1Hex(signStr);
            }
            
            log.debug("加密后字符串：{}", hashedStr);

            // 第三步：将字符串转换为大写
            String signature = hashedStr.toUpperCase();
            
            log.debug("最终signature：{}", signature);

            return signature;

        } catch (Exception e) {
            log.error("生成signature失败", e);
            throw new RuntimeException("生成signature失败：" + e.getMessage(), e);
        }
    }

    /**
     * 验证signature是否正确
     *
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @param webToken 客户唯一标识
     * @param imUserKey WebIM用户识别密钥
     * @param signature 待验证的签名
     * @param algorithm 加密算法
     * @return 是否验证通过
     */
    public static boolean verifySignature(String nonce, String timestamp, String webToken,
                                        String imUserKey, String signature, EncryptionAlgorithm algorithm) {
        try {
            String expectedSignature = generateSignatureInternal(nonce, timestamp, webToken, imUserKey, algorithm);
            return expectedSignature.equals(signature);
        } catch (Exception e) {
            log.error("验证signature失败", e);
            return false;
        }
    }
}
