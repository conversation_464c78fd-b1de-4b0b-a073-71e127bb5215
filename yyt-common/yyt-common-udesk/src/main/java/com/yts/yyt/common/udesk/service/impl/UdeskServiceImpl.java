package com.yts.yyt.common.udesk.service.impl;

import cn.hutool.json.JSONUtil;
import com.yts.yyt.common.udesk.config.UdeskWebImProperties;
import com.yts.yyt.common.udesk.dto.*;
import com.yts.yyt.common.udesk.service.UdeskService;
import com.yts.yyt.common.udesk.util.UdeskWebImSignatureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * Udesk服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UdeskServiceImpl implements UdeskService {

    private final UdeskWebImProperties udeskWebImProperties;

    @Override
    public GenerateSignatureResponse generateWebImSignature(GenerateSignatureRequest request) {
        log.info("开始生成WebIM signature，请求参数：{}", JSONUtil.toJsonStr(request));
        try {
            // 验证请求参数
            if (request == null) {
                throw new IllegalArgumentException("请求参数不能为空");
            }
            // 生成signature
            UdeskWebImSignatureUtil.SignatureResult result = UdeskWebImSignatureUtil.generateSignature(
                    request.getWebToken(),
                    udeskWebImProperties.getImUserKey());
            // 构建响应
            GenerateSignatureResponse response = new GenerateSignatureResponse(
                    result.getSignature(),
                    result.getNonce(),
                    result.getTimestamp(),
                    result.getWebToken(),
                    result.getEncryptionAlgorithm()
            );
            log.info("生成WebIM signature成功：{}", result.getSignature());
            return response;
        } catch (Exception e) {
            log.error("生成WebIM signature异常", e);
            throw new RuntimeException("生成WebIM signature失败：" + e.getMessage(), e);
        }
    }
}
