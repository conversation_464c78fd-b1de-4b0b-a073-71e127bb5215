package com.yts.yyt.common.udesk.config;

import com.yts.yyt.common.udesk.service.UdeskService;
import com.yts.yyt.common.udesk.service.impl.UdeskServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Udesk自动配置类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Configuration
@EnableConfigurationProperties(UdeskWebImProperties.class)
public class UdeskAutoConfiguration {

    /**
     * 注册UdeskService Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public UdeskService udeskService(UdeskWebImProperties properties) {
        return new UdeskServiceImpl(properties);
    }
}
