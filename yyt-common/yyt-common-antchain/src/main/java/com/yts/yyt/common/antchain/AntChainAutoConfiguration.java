package com.yts.yyt.common.antchain;

import com.alipay.mychain.taas.api.common.VersionCode;
import com.alipay.mychain.taas.api.enums.SystemCodeEnum;
import com.alipay.mychain.taas.common.client.TaasClient;
import com.alipay.mychain.taas.common.factory.ServiceFactory;
import com.alipay.mychain.taas.sdk.service.*;
import com.yts.yyt.common.antchain.config.AntChainProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Base64;

@Configuration
@EnableConfigurationProperties(AntChainProperties.class)
public class AntChainAutoConfiguration {

    @Autowired
    private AntChainProperties antChainProperties;

    @Bean
    public TaasClient buildTaasClient(){
        TaasClient client = TaasClient.newBuilder()
                .appId(antChainProperties.getAppId())
                .host(antChainProperties.getHost())
                .priKey(decoder(antChainProperties.getSecretKey()))
                .versionCode(VersionCode.V_COMMON)
                .systemCode(SystemCodeEnum.UNIVERSAL_SYSTEM.name())
                //.options(ClientOptions.newBuilder().readTimeOut(10L).connectTimeOut(10L).build())
                .build();
        return client;
    }

    private String decoder(String key) {
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] decodedBytes = decoder.decode(key);
        String decodedString = new String(decodedBytes);
        return decodedString;
    }

    @Bean
    public TsBatchService buildTsBatchService(TaasClient client) {
        TsBatchService tsBatchService = ServiceFactory.register(TsBatchService.class, client);
        return tsBatchService;
    }

    @Bean
    public TraceCodeService buildTraceCodeService(TaasClient client) {
        TraceCodeService codeService = ServiceFactory.register(TraceCodeService.class, client);
        return  codeService;
    }

    @Bean
    public TraceCoreService buildTraceCoreService(TaasClient client) {
        TraceCoreService coreService = ServiceFactory.register(TraceCoreService.class, client);
        return coreService;
    }

    @Bean
    public TraceLogisticsService buildTraceLogisticsService(TaasClient client) {
        TraceLogisticsService logisticsService = ServiceFactory.register(TraceLogisticsService.class, client);
        return logisticsService;
    }

    @Bean
    public TraceMerchantService buildTraceMerchantService(TaasClient client) {
        TraceMerchantService merchantService = ServiceFactory.register(TraceMerchantService.class, client);
        return merchantService;
    }

    @Bean
    public TraceMiniTemplateService buildTraceMiniTemplateService(TaasClient client) {
        TraceMiniTemplateService miniTemplateService = ServiceFactory.register(TraceMiniTemplateService.class, client);
        return miniTemplateService;
    }

    @Bean
    public TsPhaseService buildTsPhaseService(TaasClient client) {
        TsPhaseService tsPhaseService = ServiceFactory.register(TsPhaseService.class, client);
        return tsPhaseService;
    }

    @Bean
    public TraceProductService buildTraceProductService(TaasClient client) {
        TraceProductService productService = ServiceFactory.register(TraceProductService.class, client);
        return productService;
    }

    @Bean
    public TraceRegisterService buildTraceRegisterService(TaasClient client) {
        TraceRegisterService registerService = ServiceFactory.register(TraceRegisterService.class, client);
        return registerService;
    }


    @Bean
    public TraceRuleService buildTraceRuleService(TaasClient client) {
        TraceRuleService ruleService = ServiceFactory.register(TraceRuleService.class, client);
        return ruleService;
    }

    @Bean
    public TraceShopGuideService buildTraceShopGuideService(TaasClient client) {
        TraceShopGuideService shopGuideService = ServiceFactory.register(TraceShopGuideService.class, client);
        return shopGuideService;
    }

    @Bean
    public TraceStatService buildTraceStatService(TaasClient client) {
        TraceStatService statService = ServiceFactory.register(TraceStatService.class, client);
        return statService;
    }

    @Bean
    public TraceUserService buildTraceUserService(TaasClient client) {
        TraceUserService userService = ServiceFactory.register(TraceUserService.class, client);
        return userService;
    }

    @Bean
    public TraceFileUploadService buildTraceFileUploadService(TaasClient client) {
        TraceFileUploadService traceFileUploadService = ServiceFactory.register(TraceFileUploadService.class, client);
        return traceFileUploadService;
    }
}
