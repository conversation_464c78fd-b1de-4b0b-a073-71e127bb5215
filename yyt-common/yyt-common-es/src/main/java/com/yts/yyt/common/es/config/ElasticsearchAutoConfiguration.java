package com.yts.yyt.common.es.config;

import jakarta.annotation.PostConstruct;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import com.yts.yyt.common.es.properties.EsProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties(EsProperties.class)
public class ElasticsearchAutoConfiguration {

    private final EsProperties esProperties;

    @Value("${spring.profiles.active}")
    private String env;

    @Bean
    public RestHighLevelClient buildEsClient() {

        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));

        // 创建一个RestHighLevelClient实例
        return new RestHighLevelClient(
                // space-12345678.ap-guangzhou.qcloudes.com为空间地址
                RestClient.builder(
                            new HttpHost(esProperties.getHostname(), esProperties.getPort(), esProperties.getScheme())
                        )
                        .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder
                                .setDefaultCredentialsProvider(credentialsProvider))
        );
    }

    @PostConstruct
    public void setEnv() {
        for (EsIndexEnum value : EsIndexEnum.values()) {
            value.setEnv(this.env);
        }
    }
}
