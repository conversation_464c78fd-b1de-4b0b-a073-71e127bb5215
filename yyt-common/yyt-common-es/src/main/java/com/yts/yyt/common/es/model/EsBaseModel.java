package com.yts.yyt.common.es.model;

import com.yts.yyt.common.es.config.EsIndexEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@Slf4j
public abstract class EsBaseModel {

    /**
     * 索引
     */
    private final EsIndexEnum index;

    /**
     * id
     */
    private String id;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public EsBaseModel(EsIndexEnum index) {
        this.index = index;
    }

    public Map<String, Object> toMap() {

        Map<String, Object> map = new HashMap<>();
        map.put("id", Long.valueOf(this.getId()));
        if (Objects.nonNull(this.getCreateTime())) {
            map.put("createTime", this.getCreateTime().toEpochSecond(ZoneOffset.ofHours(8)));
        }
        if (Objects.nonNull(this.getUpdateTime())) {
            map.put("updateTime", this.getUpdateTime().toEpochSecond(ZoneOffset.ofHours(8)));
        }

        try {
            PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(this.getClass());
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                Method readMethod = propertyDescriptor.getReadMethod();
                // 获取子类对象所有不为null的字段值
                if (readMethod.getDeclaringClass() == this.getClass()) {
                    Object value = readMethod.invoke(this);
                    if (Objects.nonNull(value)) {
                        if (value instanceof LocalDateTime dateTime) {
                            map.put(propertyDescriptor.getName(), dateTime.toEpochSecond(ZoneOffset.ofHours(8)));
                        } else {
                            map.put(propertyDescriptor.getName(), value);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("{}转化map错误。", this, e);
        }

        return map;
    }
}
