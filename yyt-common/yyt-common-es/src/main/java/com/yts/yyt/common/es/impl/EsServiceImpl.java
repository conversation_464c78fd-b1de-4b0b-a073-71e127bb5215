package com.yts.yyt.common.es.impl;

import com.yts.yyt.common.es.EsService;
import com.yts.yyt.common.es.model.EsBaseModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;

import java.util.Collections;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class EsServiceImpl implements EsService {

    private final RestHighLevelClient esClient;

    @Override
    public boolean createIndex(EsBaseModel model) {
        return batchCreateIndex(Collections.singletonList(model));
    }

    @Override
    public boolean deleteIndex(EsBaseModel model) {
        return batchDeleteIndex(Collections.singletonList(model));
    }

    @Override
    public boolean updateIndex(EsBaseModel model) {
        return batchUpdateIndex(Collections.singletonList(model));
    }

    @Override
    public boolean batchCreateIndex(List<? extends EsBaseModel> list) {
        BulkRequest bulkRequest = new BulkRequest();
        for (EsBaseModel model : list) {
            IndexRequest indexRequest = new IndexRequest();
            indexRequest.index(model.getIndex().getIndexName());
            indexRequest.id(model.getId());
            indexRequest.source(model.toMap());
            bulkRequest.add(indexRequest);
        }
        return bulk(bulkRequest);
    }

    @Override
    public boolean batchDeleteIndex(List<? extends EsBaseModel> list) {
        BulkRequest bulkRequest = new BulkRequest();
        for (EsBaseModel model : list) {
            DeleteRequest deleteRequest = new DeleteRequest();
            deleteRequest.index(model.getIndex().getIndexName());
            deleteRequest.id(model.getId());
            bulkRequest.add(deleteRequest);
        }
        return bulk(bulkRequest);
    }

    @Override
    public boolean batchUpdateIndex(List<? extends EsBaseModel> list) {
        BulkRequest bulkRequest = new BulkRequest();
        for (EsBaseModel model : list) {
            UpdateRequest updateRequest = new UpdateRequest();
            updateRequest.index(model.getIndex().getIndexName());
            updateRequest.id(model.getId());
            updateRequest.doc(model.toMap());
            bulkRequest.add(updateRequest);
        }
        return bulk(bulkRequest);
    }

    @Override
    public SearchResponse search(SearchRequest searchRequest) {
        try {
            return esClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("请求es失败，错误：", e);
            return null;
        }
    }

    public boolean bulk(BulkRequest bulkRequest) {
        try {
            BulkResponse responses = esClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (responses.hasFailures()) {
                log.error("请求es失败，错误：{}", responses.buildFailureMessage());
            } else {
                return true;
            }
        } catch (Exception e) {
            log.error("请求es失败，错误：", e);
        }
        return false;
    }
}
