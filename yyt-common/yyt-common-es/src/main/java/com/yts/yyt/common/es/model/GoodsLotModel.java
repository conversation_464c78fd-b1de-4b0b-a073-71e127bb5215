package com.yts.yyt.common.es.model;

import com.yts.yyt.common.es.config.EsIndexEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
public class GoodsLotModel extends EsBaseModel {

    /**
     * 商品名称
     */
    private String name;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺头像
     */
    private String shopAvatar;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 藏品id
     */
    private Long goodsId;

    /**
     * 商品品相
     */
    private String goodsCondition;

    /**
     * 商品断代
     */
    private String goodsEra;

    /**
     * 藏品图片
     */
    private String mainImage;

    /**
     * 商品销售价
     */
    private BigDecimal salePrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品状态
     */
    private String state;
    /**
     * 分销状态
     */
    private Integer distEnable;

    /**
     * 商家权益等级配置id
     */
    private String benefitLevel;

    /**
     * 权益等级名称
     */
    private String tranName;

    /**
     * 权益等级编码
     */
    private String tranCode;

    /**
     * 权益等级logo
     */
    private String tranIcon;

    /**
     * 排序值
     */
    private Integer sort;

    public GoodsLotModel() {
        super(EsIndexEnum.GOODS_LOT);
    }
}
