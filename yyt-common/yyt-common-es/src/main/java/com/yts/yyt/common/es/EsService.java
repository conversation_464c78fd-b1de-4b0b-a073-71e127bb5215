package com.yts.yyt.common.es;

import com.yts.yyt.common.es.model.EsBaseModel;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;

import java.util.List;

public interface EsService {

    /**
     * 创建藏品索引
     * @param model
     * @return
     */
    boolean createIndex(EsBaseModel model);

    /**
     * 删除藏品索引
     * @param model
     * @return
     */
    boolean deleteIndex(EsBaseModel model);

    /**
     * 更新藏品索引
     * @param model
     * @return
     */
    boolean updateIndex(EsBaseModel model);

    /**
     * 批量创建藏品索引
     * @param list
     * @return
     */
    boolean batchCreateIndex(List<? extends EsBaseModel> list);

    /**
     * 批量删除藏品索引
     * @return
     */
    boolean batchDeleteIndex(List<? extends EsBaseModel> list);

    /**
     * 批量删除藏品索引
     * @return
     */
    boolean batchUpdateIndex(List<? extends EsBaseModel> list);

    /**
     * 搜索
     * @param searchRequest
     * @return
     */
    SearchResponse search(SearchRequest searchRequest);

    /**
     * 批量操作
     * @param bulkRequest
     * @return
     */
    boolean bulk(BulkRequest bulkRequest);

}
