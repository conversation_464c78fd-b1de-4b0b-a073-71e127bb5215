package com.yts.yyt.common.es.model;

import com.yts.yyt.common.es.config.EsIndexEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
public class StoreGoodsModel extends EsBaseModel {

    /**
     * 商品名称
     */
    private String name;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺头像
     */
    private String shopAvatar;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商品品相
     */
    private String goodsCondition;

    /**
     * 商品断代
     */
    private String goodsEra;

    /**
     * 藏品图片
     */
    private String mainImage;

    /**
     * 商品销售价
     */
    private BigDecimal salePrice;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品状态
     */
    private String state;

    /**
     * 商家权益等级配置id
     */
    private String benefitLevel;

    /**
     * 权益等级名称
     */
    private String tranName;

    /**
     * 权益等级编码
     */
    private String tranCode;

    /**
     * 权益等级logo
     */
    private String tranIcon;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 公示结束时间
     */
    private LocalDateTime publicityEndTime;

    public StoreGoodsModel() {
        super(EsIndexEnum.STORE_GOODS);
    }
}
